<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="优惠卷"></CustomNavBar>
    </template>
    <view class="coupons-box">
      <!-- 分类标题 -->
      <view class="section-title">未使用</view>

      <!-- 未使用优惠券列表 -->
      <view class="coupons-list">
        <view v-for="(coupon, index) in unusedCoupons" :key="index" class="coupon-card">
          <view class="coupon-left">
            <!-- 优惠券金额 -->
            <view class="coupon-amount">
              <text class="currency">¥</text>
              <text class="amount">{{ coupon.amount }}</text>
            </view>
          </view>

          <view class="coupon-divider"></view>

          <view class="coupon-right">
            <!-- 优惠券信息 -->
            <view class="coupon-info">
              <view class="coupon-title">{{ coupon.title }}</view>
              <view class="coupon-desc">{{ coupon.description }}</view>
            </view>

            <!-- 使用按钮 -->
            <view class="coupon-action">
              <wd-button size="small" custom-class="use-btn" @click="useCoupon(coupon)">
                立即使用
              </wd-button>
            </view>
          </view>

          <!-- 有效期 -->
          <view class="coupon-validity">有效期至{{ coupon.validUntil }}</view>
        </view>
      </view>

      <!-- 已使用分类 -->
      <view class="section-title">已使用</view>
      <view class="coupons-list">
        <view v-for="(coupon, index) in usedCoupons" :key="index" class="coupon-card used-card">
          <view class="coupon-left">
            <view class="coupon-amount">
              <text class="currency">¥</text>
              <text class="amount">{{ coupon.amount }}</text>
            </view>
          </view>

          <view class="coupon-divider"></view>

          <view class="coupon-right">
            <view class="coupon-info">
              <view class="coupon-title">{{ coupon.title }}</view>
              <view class="coupon-desc">{{ coupon.description }}</view>
            </view>

            <view class="coupon-action">
              <view class="status-text">已使用</view>
            </view>
          </view>

          <view class="coupon-validity">使用时间{{ coupon.usedTime }}</view>
        </view>
      </view>

      <!-- 已过期分类 -->
      <view class="section-title">已过期</view>
      <view class="coupons-list">
        <view
          v-for="(coupon, index) in expiredCoupons"
          :key="index"
          class="coupon-card expired-card"
        >
          <view class="coupon-left">
            <view class="coupon-amount">
              <text class="currency">¥</text>
              <text class="amount">{{ coupon.amount }}</text>
            </view>
          </view>

          <view class="coupon-divider"></view>

          <view class="coupon-right">
            <view class="coupon-info">
              <view class="coupon-title">{{ coupon.title }}</view>
              <view class="coupon-desc">{{ coupon.description }}</view>
            </view>

            <view class="coupon-action">
              <view class="status-text">已过期</view>
            </view>
          </view>

          <view class="coupon-validity">有效期至{{ coupon.validUntil }}</view>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 页面样式配置
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 模拟优惠券数据
const couponsData = ref([
  {
    id: 1,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '满500减52',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 2,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '无门槛使用',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 3,
    amount: 8.5,
    title: '神马作弊卡-折扣券',
    description: '无门槛折扣',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 4,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '无门槛使用',
    validUntil: '2023.05.09 23:59:59',
    usedTime: '2023.05.09 23:59:59',
    status: 'used',
  },
  {
    id: 5,
    amount: 30,
    title: '神马作弊卡-抵扣券',
    description: '满200使用',
    validUntil: '2023.04.15 23:59:59',
    status: 'expired',
  },
  {
    id: 6,
    amount: 15,
    title: '神马作弊卡-折扣券',
    description: '无门槛折扣',
    validUntil: '2023.03.20 23:59:59',
    status: 'expired',
  },
])

// 未使用的优惠券
const unusedCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 'unused')
})

// 已使用的优惠券
const usedCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 'used')
})

// 已过期的优惠券
const expiredCoupons = computed(() => {
  return couponsData.value.filter((coupon) => coupon.status === 'expired')
})

// 使用优惠券
const useCoupon = (coupon: any) => {
  // 这里可以调用使用优惠券的API
  console.log('使用优惠券:', coupon)
  // 模拟使用成功后更新状态
  coupon.status = 'used'
  coupon.usedTime = '2023.05.09 23:59:59'
}
</script>

<style scoped lang="scss">
.coupons-box {
  padding: 0rpx 40rpx;
}

// 分类标题样式
.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin: 40rpx 0 30rpx 0;

  &:first-child {
    margin-top: 20rpx;
  }
}

// 优惠券列表样式
.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

// 优惠券卡片样式
.coupon-card {
  position: relative;
  background: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0rpx 8rpx 29rpx 0rpx rgba(0, 0, 0, 0.1);
  display: flex;
  min-height: 160rpx;

  &.used-card,
  &.expired-card {
    opacity: 0.6;

    .coupon-amount {
      color: #999999;
    }

    .coupon-info {
      .coupon-title,
      .coupon-desc {
        color: #999999;
      }
    }
  }
}

// 优惠券左侧（金额区域）
.coupon-left {
  width: 200rpx;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff4545 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  .used-card &,
  .expired-card & {
    background: #cccccc;
  }
}

// 优惠券金额样式
.coupon-amount {
  display: flex;
  align-items: baseline;
  color: #ffffff;

  .currency {
    font-size: 28rpx;
    font-weight: 500;
  }

  .amount {
    font-size: 56rpx;
    font-weight: bold;
    margin-left: 4rpx;
  }
}

// 分割线（虚线效果）
.coupon-divider {
  width: 2rpx;
  background: repeating-linear-gradient(
    to bottom,
    #e0e0e0 0rpx,
    #e0e0e0 10rpx,
    transparent 10rpx,
    transparent 20rpx
  );
  position: relative;

  &::before,
  &::after {
    content: '';
    position: absolute;
    width: 20rpx;
    height: 20rpx;
    background: linear-gradient(125deg, #ffdede 0%, #ebeffa 20%, #ffffff 100%);
    border-radius: 50%;
    left: 50%;
    transform: translateX(-50%);
  }

  &::before {
    top: -10rpx;
  }

  &::after {
    bottom: -10rpx;
  }
}

// 优惠券右侧（信息区域）
.coupon-right {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

// 优惠券信息样式
.coupon-info {
  flex: 1;

  .coupon-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    line-height: 1.2;
    margin-bottom: 8rpx;
  }

  .coupon-desc {
    font-size: 24rpx;
    color: #666666;
    line-height: 1.2;
  }
}

// 优惠券操作区域样式
.coupon-action {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 20rpx;
}

// 使用按钮样式
:deep(.use-btn) {
  background: #ff4545 !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 30rpx !important;
  padding: 12rpx 32rpx !important;
  font-size: 24rpx !important;
  font-weight: 500 !important;
  min-width: 140rpx !important;
  height: 56rpx !important;
}

// 状态文字样式
.status-text {
  font-size: 24rpx;
  padding: 12rpx 32rpx;
  border-radius: 30rpx;
  text-align: center;
  min-width: 140rpx;
  background: #f5f5f5;
  color: #999999;
  height: 56rpx;
  line-height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

// 有效期样式
.coupon-validity {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.05);
  padding: 8rpx 30rpx;
  font-size: 20rpx;
  color: #999999;
  text-align: center;
}
</style>
