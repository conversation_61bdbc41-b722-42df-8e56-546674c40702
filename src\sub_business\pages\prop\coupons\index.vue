<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="优惠卷"></CustomNavBar>
    </template>
    <view class="coupons-box">
      <!-- 状态分类标签 -->
      <view class="status-tabs">
        <view
          v-for="(tab, index) in statusTabs"
          :key="index"
          class="status-tab"
          :class="{ active: activeTab === index }"
          @click="switchTab(index)"
        >
          {{ tab.name }}
        </view>
      </view>

      <!-- 优惠券列表 -->
      <view class="coupons-list">
        <view
          v-for="(coupon, index) in currentCoupons"
          :key="index"
          class="coupon-card"
          :class="{ expired: coupon.status === 'expired', used: coupon.status === 'used' }"
        >
          <!-- 优惠券金额 -->
          <view class="coupon-amount">
            <text class="currency">¥</text>
            <text class="amount">{{ coupon.amount }}</text>
          </view>

          <!-- 优惠券信息 -->
          <view class="coupon-info">
            <view class="coupon-title">{{ coupon.title }}</view>
            <view class="coupon-desc">{{ coupon.description }}</view>
            <view class="coupon-validity">有效期至{{ coupon.validUntil }}</view>
          </view>

          <!-- 使用按钮 -->
          <view class="coupon-action">
            <wd-button
              v-if="coupon.status === 'unused'"
              size="small"
              custom-class="use-btn"
              @click="useCoupon(coupon)"
            >
              立即使用
            </wd-button>
            <view v-else-if="coupon.status === 'used'" class="status-text used-text">已使用</view>
            <view v-else class="status-text expired-text">已过期</view>
          </view>
        </view>

        <!-- 空状态 -->
        <view v-if="currentCoupons.length === 0" class="empty-state">
          <text class="empty-text">暂无{{ statusTabs[activeTab].name }}的优惠券</text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 页面样式配置
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 状态标签配置
const statusTabs = ref([
  { name: '未使用', value: 'unused' },
  { name: '已使用', value: 'used' },
  { name: '已过期', value: 'expired' },
])

// 当前选中的标签
const activeTab = ref(0)

// 模拟优惠券数据
const couponsData = ref([
  {
    id: 1,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '满500使用',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 2,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '无门槛使用',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 3,
    amount: 8.5,
    title: '神马作弊卡-折扣券',
    description: '无门槛折扣',
    validUntil: '2023.05.09 23:59:59',
    status: 'unused',
  },
  {
    id: 4,
    amount: 52,
    title: '神马作弊卡-抵扣券',
    description: '无门槛使用',
    validUntil: '2023.05.09 23:59:59',
    status: 'used',
  },
])

// 当前显示的优惠券列表
const currentCoupons = computed(() => {
  const currentStatus = statusTabs.value[activeTab.value].value
  return couponsData.value.filter((coupon) => coupon.status === currentStatus)
})

// 切换标签
const switchTab = (index: number) => {
  activeTab.value = index
}

// 使用优惠券
const useCoupon = (coupon: any) => {
  // 这里可以调用使用优惠券的API
  console.log('使用优惠券:', coupon)
  // 模拟使用成功后更新状态
  coupon.status = 'used'
}
</script>

<style scoped lang="scss">
.coupons-box {
  padding: 0rpx 40rpx;
}

// 状态标签样式
.status-tabs {
  display: flex;
  margin-bottom: 40rpx;
  background: transparent;
}

.status-tab {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #999999;
  position: relative;

  &.active {
    color: #333333;
    font-weight: 500;

    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 60rpx;
      height: 4rpx;
      background: #ff4545;
      border-radius: 2rpx;
    }
  }
}

// 优惠券列表样式
.coupons-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

// 优惠券卡片样式
.coupon-card {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0rpx 8rpx 29rpx 0rpx rgba(0, 0, 0, 0.1);
  position: relative;

  &.used,
  &.expired {
    opacity: 0.6;

    .coupon-amount {
      color: #999999;
    }

    .coupon-info {
      color: #999999;
    }
  }
}

// 优惠券金额样式
.coupon-amount {
  display: flex;
  align-items: baseline;
  margin-right: 30rpx;
  color: #ff4545;

  .currency {
    font-size: 24rpx;
    font-weight: 500;
  }

  .amount {
    font-size: 48rpx;
    font-weight: bold;
    margin-left: 4rpx;
  }
}

// 优惠券信息样式
.coupon-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;

  .coupon-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333333;
    line-height: 1.2;
  }

  .coupon-desc {
    font-size: 24rpx;
    color: #666666;
    line-height: 1.2;
  }

  .coupon-validity {
    font-size: 20rpx;
    color: #999999;
    line-height: 1.2;
    margin-top: 4rpx;
  }
}

// 优惠券操作区域样式
.coupon-action {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
}

// 使用按钮样式
:deep(.use-btn) {
  background: #ff4545 !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 20rpx !important;
  padding: 16rpx 32rpx !important;
  font-size: 24rpx !important;
  font-weight: 500 !important;
  min-width: 120rpx !important;
  height: 60rpx !important;
}

// 状态文字样式
.status-text {
  font-size: 24rpx;
  padding: 16rpx 32rpx;
  border-radius: 20rpx;
  text-align: center;
  min-width: 120rpx;

  &.used-text {
    background: #f5f5f5;
    color: #999999;
  }

  &.expired-text {
    background: #f5f5f5;
    color: #999999;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 100rpx 0;

  .empty-text {
    font-size: 28rpx;
    color: #999999;
  }
}
</style>
