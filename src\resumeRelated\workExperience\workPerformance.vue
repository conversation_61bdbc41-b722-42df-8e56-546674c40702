<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
    disableScroll: true,
    'app-plus': {
      softinputMode: 'adjustResize',
    },
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="工作业绩">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
        <template #right>
          <view @click="submit">确认</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-textarea
        clear-trigger="focus"
        v-model="workPerformance"
        :maxlength="1000"
        clearable
        show-word-limit
        focus
        custom-class="custom-class"
        custom-textarea-container-class="custom-textarea-container-class"
        custom-textarea-class="custom-textarea-class"
      />
    </view>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { useResumeStore } from '@/store'
import { useMessage } from 'wot-design-uni'
// 公司
const resumeStore = useResumeStore()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const workPerformance = ref('')
// 初始化
const initNane = ref('')
const message = useMessage()
const submit = () => {
  resumeStore.setWorkPerformance(workPerformance.value)
  uni.navigateBack()
}
onLoad(async (options) => {
  await nextTick()
  workPerformance.value = options.workPerformance
  initNane.value = options.workPerformance
})
// 返回
const back = () => {
  if (workPerformance.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        resumeStore.setWorkPerformance(initNane.value)
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
.corporateName {
  padding: 40rpx 40rpx;
}
::v-deep .custom-class {
  border-radius: 20rpx !important;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}
::v-deep .custom-textarea-container-class {
  height: calc(100vh - 350rpx);
}
::v-deep .wd-textarea__inner {
  height: calc(100vh - 430rpx);
}
</style>
