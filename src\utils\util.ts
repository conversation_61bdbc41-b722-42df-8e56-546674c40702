/** 数组去重 */
export const arrayRemoveRepeat = <T>(ary: T[]): T[] => {
  return ary.filter((item, key) => ary.indexOf(item) === key)
}

// 随机整数，大于等于80，小于等于99
export const randomInt = (min: number, max: number) => {
  return Math.floor(Math.random() * (max - min + 1)) + min
}

// 文本截断函数 - 超过指定字数显示省略号
export const truncateText = (text: string, maxLength: number = 6): string => {
  if (!text) return ''
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

/** 字符串按指定分隔符分割转数组 */
export const splitToArray = (str: string, separator: string = ',') => {
  if (!str) return []
  return str.split(separator).filter(Boolean)
}
/**
 * 将数字转换为k格式（大于1000的转为k）
 * @param num 需要转换的数字
 * @returns 格式化后的字符串
 */
export const formatToKilo = (num: number) => {
  return num >= 1000 ? `${~~(num / 100) / 10}k` : num
}
/** 工作经验文本 */
export const workExperienceText = (start: number, end: number) => {
  if (!start && !end) return '不限'
  const validValues = Array.from(new Set([start, end].filter(Boolean)))
  return `${validValues.join('-')}年工作经验`
}

// 距离处理
export const distanceHandle = (distance: number | string) => {
  const distanceNum = Math.floor(parseInt(String(distance)) / 1000)
  if (distanceNum) {
    if (distance === 0) {
      return '<1km'
    } else {
      return distance + 'km'
    }
  }
}

/**
 * 格式化薪资信息
 * @param workSalaryBegin 薪资范围-最低
 * @param workSalaryEnd 薪资范围-最高
 * @param salaryMonths 薪资月数
 * @returns 格式化后的薪资字符串
 */
export const formatSalary = (
  workSalaryBegin: number,
  workSalaryEnd: number,
  salaryMonths: number,
) => {
  const salaryRange =
    [formatToKilo(workSalaryBegin), formatToKilo(workSalaryEnd)].filter(Boolean).join('-') || '面议'
  const salaryMonthsText = salaryMonths > 12 ? `${salaryMonths}薪` : ''
  return [salaryRange, salaryMonthsText].filter(Boolean).join(' · ')
}
