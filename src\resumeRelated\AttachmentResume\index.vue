<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="附件简历"></CustomNavBar>
    </template>
    <view class="box">
      <view class="box-list">
        <view
          class="box-list-item flex-between m-b-30rpx"
          v-for="(item, index) in fileList"
          :key="index"
        >
          <view class="flex-c" @click="previewPdf(item)">
            <wd-img :width="38" :height="38" :src="pdf" />
            <view class="m-l-30rpx">
              <view class="text-28rpx c-#333333 p-b-10rpx">{{ item.fileName }}</view>
              <view class="text-24rpx c-#888888">{{ item.createTime }}</view>
            </view>
          </view>
          <wd-img :width="20" :height="20" :src="del" @click.stop="delpdf(item.id)"></wd-img>
        </view>
        <view class="box-list-item flex-between m-b-30rpx">
          <view class="text-28rpx c-#333333">上传新附件({{ fileList.length }}/3)</view>
          <view v-if="fileList.length >= 3"></view>
          <view v-else>
            <wd-img :width="20" :height="20" :src="addpdf" @click="showPdfUploadModal"></wd-img>
          </view>
        </view>
      </view>
    </view>

    <!-- PDF上传模态框 -->
    <view v-if="showUploadModal" class="upload-modal-overlay" @click="hidePdfUploadModal">
      <view class="upload-modal" @click.stop>
        <view class="modal-header">
          <view class="modal-title">上传PDF文件</view>
          <view class="modal-close" @click="hidePdfUploadModal">×</view>
        </view>
        <view class="modal-content">
          <web-view
            v-if="webviewUrl"
            :src="webviewUrl"
            @message="handleWebViewMessage"
            @load="handleWebViewLoad"
            class="pdf-webview"
            id="webview"
          />
          <view v-if="webviewLoading" class="loading-overlay">
            <view class="loading-content">
              <text class="loading-text">正在加载PDF上传页面...</text>
            </view>
          </view>
          <view v-if="webviewError" class="error-overlay">
            <view class="error-content">
              <text class="error-text">{{ webviewError }}</text>
              <button @click="retryLoad" class="retry-btn">重试</button>
            </view>
          </view>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box">
          <view class="btn_bg" @click="GenerateResume">生成简历附件</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import pdf from '@/resumeRelated/img/pdfimg.png'
import del from '@/resumeRelated/img/del.png'
import addpdf from '@/resumeRelated/img/addpdf.png'
import { queryMyFileResumeList, deleteFileResume, addFileResume } from '@/interPost/resume'
import { useMessage } from 'wot-design-uni'
import { useUserInfo } from '@/hooks/common/useUserInfo'
import { usePaging } from '@/hooks/common/usePaging'

const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const message = useMessage()

// WebView相关状态
const showUploadModal = ref(false)
const webviewUrl = ref('')
const webviewLoading = ref(false)
const webviewError = ref('')
const webviewContext = ref<any>(null)

// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})

// PDF上传路径
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'

onShow(() => {
  getList()
})

onMounted(() => {
  // 检测当前环境
  // #ifdef APP-PLUS
  if ((uni as any).getEnv) {
    ;(uni as any).getEnv((res: any) => {
      console.log('当前环境:', res)
    })
  }
  // #endif
})

const fileList = ref([])

// 获取列表
const getList = async () => {
  const res: any = await queryMyFileResumeList()
  if (res.code === 0) {
    fileList.value = res.data
  }
}

// 查看pdf
const previewPdf = (item: any) => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/WebViewpdf?fileName=${item.fileName}&fileUrl=${item.fileUrl}`,
  })
}

// 删除
const delpdf = async (id: any) => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      deleteFileResume({ id }).then((res: any) => {
        if (res.code === 0) {
          getList()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}
// 创建WebView上下文 (APP环境专用)
const createWebViewContext = () => {
  // #ifdef APP-PLUS
  try {
    if (typeof uni !== 'undefined' && (uni as any).createWebViewContext) {
      webviewContext.value = (uni as any).createWebViewContext('webview')
      console.log('WebView上下文创建成功')
    }
  } catch (error) {
    console.error('创建WebView上下文失败:', error)
  }
  // #endif
}

// 处理WebView消息 - 增强兼容性
const handleWebViewMessage = (event: any) => {
  try {
    console.log('收到WebView原始消息:', JSON.stringify(event))

    // 处理不同平台的消息格式
    let messages = []

    // 处理uni-app格式消息 (APP环境)
    if (event.detail && event.detail.data) {
      messages = Array.isArray(event.detail.data) ? event.detail.data : [event.detail.data]
    }
    // 处理标准postMessage格式 (H5环境)
    else if (event.data) {
      messages = Array.isArray(event.data) ? event.data : [event.data]
    }
    // 处理微信小程序等特殊格式
    else if (event.message && event.message.data) {
      messages = Array.isArray(event.message.data) ? event.message.data : [event.message.data]
    }

    console.log('解析后的消息数组:', JSON.stringify(messages))

    // 递归处理嵌套数组结构
    const processMessages = (msgArray: any[]) => {
      msgArray.forEach((msg: any) => {
        // 如果是嵌套数组，递归处理
        if (Array.isArray(msg)) {
          processMessages(msg)
          return
        }

        // 处理字符串格式的消息
        if (typeof msg === 'string') {
          try {
            msg = JSON.parse(msg)
          } catch (e) {
            console.warn('消息解析失败:', msg)
            return
          }
        }

        if (msg && msg.type) {
          console.log(`处理消息类型: ${msg.type}`)
          handleSingleMessage(msg)
        } else {
          console.warn('无效的消息格式:', msg)
        }
      })
    }

    // 处理每个消息
    processMessages(messages)
  } catch (error) {
    console.error('处理WebView消息失败:', error)
  }
}

// 处理单个消息
const handleSingleMessage = (message: any) => {
  switch (message.type) {
    case 'WEBVIEW_READY':
      console.log('WebView已就绪')
      // 关键修复：立即发送配置
      handleWebViewReady()
      break

    case 'FILE_SELECTED':
      console.log('文件已选择:', message.data)
      // 确保此时已发送配置
      if (!configSent.value) {
        console.warn('配置未发送，立即补发')
        handleWebViewReady()
      } else {
        console.log('配置已发送，可以正常上传')
      }
      break

    case 'UPLOAD_SUCCESS':
      console.log('上传成功:', message.data)
      handleUploadSuccess(message.data)
      break

    case 'UPLOAD_ERROR':
      console.log('上传失败:', message.data)
      handleUploadError(message.data)
      break

    case 'INIT_CONFIG':
      console.log('收到INIT_CONFIG确认消息')
      break

    default:
      console.warn('未知的消息类型:', message.type)
  }
}

// 跟踪配置发送状态
const configSent = ref(false)

// 处理WebView就绪 - 发送配置
const handleWebViewReady = () => {
  console.log('handleWebViewReady被调用，configSent状态:', configSent.value)

  // 强制发送配置，不管之前是否发送过
  console.log('WebView就绪，发送配置')

  const config = {
    maxFileSize: 20 * 1024 * 1024, // 20MB
    allowedTypes: ['application/pdf'],
    uploadUrl: baseUrl,
    headers: header.value,
    timeout: 300000,
    additionalData: { type: 'resume' },
  }

  console.log('发送配置到WebView:', JSON.stringify(config, null, 2))
  sendMessageToWebView({
    type: 'INIT_CONFIG',
    data: config,
  })

  configSent.value = true
  console.log('配置发送完成，configSent设置为true')
}

// 发送消息到WebView - 增强兼容性
const sendMessageToWebView = (message: any) => {
  console.log('发送消息到WebView:', JSON.stringify(message, null, 2))

  try {
    // APP环境使用uni API
    // #ifdef APP-PLUS
    if (webviewContext.value && webviewContext.value.evalJS) {
      console.log('使用evalJS发送消息')

      // 方法1: 直接调用handleMessageFromVue函数
      const jsCode1 = `
        try {
          console.log('WebView收到evalJS调用');
          if (window.handleMessageFromVue) {
            console.log('调用window.handleMessageFromVue');
            window.handleMessageFromVue(${JSON.stringify(message)});
          } else {
            console.warn('window.handleMessageFromVue未定义');
          }
        } catch(e) {
          console.error('处理Vue消息失败:', e);
        }
      `
      webviewContext.value.evalJS(jsCode1)
      console.log('通过evalJS发送消息成功')

      // 方法2: 使用postMessage作为备选方案
      setTimeout(() => {
        const jsCode2 = `
          try {
            console.log('使用postMessage发送消息');
            window.postMessage(${JSON.stringify(message)}, '*');
          } catch(e) {
            console.error('postMessage发送失败:', e);
          }
        `
        webviewContext.value.evalJS(jsCode2)
      }, 100)
    } else {
      console.error('createWebViewContext不可用或未初始化，webviewContext:', webviewContext.value)
    }
    // #endif
  } catch (error) {
    console.error('发送消息到WebView失败:', error)
  }
}

// 初始化WebView
const initWebView = () => {
  try {
    console.log('初始化WebView开始')
    webviewLoading.value = true
    webviewError.value = ''
    configSent.value = false // 重置配置发送状态
    console.log('重置configSent为false')

    const token = getToken()
    // 添加时间戳防止缓存
    const timestamp = new Date().getTime()
    webviewUrl.value = `/static/pdf-upload.html?token=${encodeURIComponent(token)}&t=${timestamp}`

    console.log('设置webviewUrl:', webviewUrl.value)

    // 创建WebView上下文
    createWebViewContext()
  } catch (error) {
    console.error('初始化WebView失败:', error)
    webviewError.value = '初始化失败，请重试'
    webviewLoading.value = false
  }
}

// 生成简历
const GenerateResume = () => {
  uni.navigateTo({
    url: `/resumeRelated/AttachmentResume/GeneratePDF`,
  })
}

// 显示PDF上传模态框
const showPdfUploadModal = () => {
  if (fileList.value.length >= 3) {
    uni.showToast({ title: '最多上传3个附件', icon: 'none' })
    return
  }

  // 强制重置所有状态
  console.log('显示PDF上传模态框，强制重置状态')
  configSent.value = false
  showUploadModal.value = true
  initWebView()
}

// 隐藏PDF上传模态框
const hidePdfUploadModal = () => {
  console.log('隐藏PDF上传模态框，重置所有状态')
  showUploadModal.value = false
  webviewUrl.value = ''
  webviewLoading.value = false
  webviewError.value = ''
  webviewContext.value = null
  configSent.value = false // 确保重置配置发送状态
}

// 处理WebView加载完成
const handleWebViewLoad = () => {
  console.log('WebView加载完成')
  webviewLoading.value = false

  // 移除自动发送配置的逻辑，等待WebView主动发送WEBVIEW_READY消息
  console.log('等待WebView发送WEBVIEW_READY消息')
}

// 处理上传成功
const handleUploadSuccess = (data: any) => {
  console.log('上传成功:', data)

  // 关闭模态框
  hidePdfUploadModal()

  // 处理不同的数据格式
  let fileId = null

  if (data.fileId) {
    fileId = data.fileId
  } else if (data.data && Array.isArray(data.data) && data.data[0] && data.data[0].fileId) {
    fileId = data.data[0].fileId
  } else if (data.data && data.data.fileId) {
    fileId = data.data.fileId
  }

  if (fileId) {
    addFilePDF(fileId)
  } else {
    uni.showToast({
      title: '上传失败，请重试',
      icon: 'error',
    })
  }
}

// 处理上传错误
const handleUploadError = (error: string) => {
  console.error('上传失败:', error)
  uni.showToast({
    title: error || '上传失败',
    icon: 'error',
  })
}

// 重试加载
const retryLoad = () => {
  webviewError.value = ''
  initWebView()
}

// 上传
const addFilePDF = async (fileId: string) => {
  const res: any = await addFileResume({ fileId })
  if (res.code === 0) {
    getList()
    uni.showToast({ title: '上传成功', icon: 'success' })
  } else {
    uni.showToast({ title: res.msg || '上传失败', icon: 'none' })
  }
}
</script>

<style lang="scss" scoped>
.box {
  padding: 40rpx 60rpx;
  .box-list {
    .box-list-item {
      padding: 30rpx 40rpx;
      background: #fff;
      border-radius: 30rpx;
      box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }
}

// 上传模态框样式
.upload-modal-overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.5);
}

.upload-modal {
  position: relative;
  z-index: 10000;
  display: flex;
  flex-direction: column;
  width: 90%;
  max-width: 600rpx;
  max-height: 80%;
  overflow: hidden;
  background: white;
  border-radius: 20rpx;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  padding: 10rpx;
  font-size: 40rpx;
  color: #999;
  cursor: pointer;
}

.modal-content {
  position: relative;
  flex: 1;
  min-height: 600rpx;
}

.pdf-webview {
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.9);
}

.loading-content {
  text-align: center;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

.error-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.95);
}

.error-content {
  padding: 40rpx;
  text-align: center;
}

.error-text {
  display: block;
  margin-bottom: 40rpx;
  font-size: 28rpx;
  color: #ff4757;
}

.retry-btn {
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  color: white;
  background-color: #007aff;
  border: none;
  border-radius: 10rpx;
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
