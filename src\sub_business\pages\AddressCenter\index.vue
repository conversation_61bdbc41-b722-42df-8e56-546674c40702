<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging :paging-style="pageStyle" @query="queryList" v-model="pageData" ref="pagingRef">
    <template #top>
      <CustomNavBar title="地址"></CustomNavBar>
    </template>
    <view class="px-60rpx py-20rpx">
      <view
        class="px-40rpx py-40rpx bg-#fff rounded-[30rpx] m-b-30rpx shadow-[8rpx_8rpx_32rpx_0rpx_rgba(0,0,0,0.1)]"
        v-for="(item, index) in pageData"
        :key="index"
        @click="handleActiveAddress(item)"
      >
        <view class="relative">
          <view class="flex items-center">
            <view class="text-28rpx c-#000">
              {{ item.provideName }}{{ item.cityName === item.provideName ? '' : item.cityName
              }}{{ item.districtName }}{{ item.address }}
              <text
                :class="{
                  'bg-#BEBEBE': item.status === 0,
                  'bg-#008000': item.status === 1,
                  'bg-#FF0000': item.status === 2,
                  'bg-#FF1000': item.status === 3,
                }"
                class="text-20rpx c-#fff text-c rounded-[3rpx] px-10rpx py-2rpx m-l-10rpx rounded=[10rpx] inline-block"
              >
                {{
                  item.status === 0
                    ? '审核中'
                    : item.status === 1
                      ? '正常'
                      : item.status === 2
                        ? '未通过'
                        : '下架'
                }}
              </text>
            </view>
          </view>

          <view
            class="absolute right-[-10rpx] top-0"
            v-if="item.status === 2 || item.status === 3"
            @click="delet(item.id)"
          >
            <wd-img :width="20" :height="20" :src="del"></wd-img>
          </view>
        </view>
      </view>
    </view>

    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box">
          <view class="btn_bg" @click="goAddressPage">添加新的地址</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import del from '@/resumeRelated/img/del.png'
import { queryListAdress, deleteAdress } from '@/service/companyAdress'
import { truncateText } from '@/utils/util'
import { useMessage } from 'wot-design-uni'

const message = useMessage()
const { pageParams } = usePagePeriod()
const { pageStyle, pageData, pageInfo, pagingRef, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const { releaseActiveAddress } = useReleasePost()

const sourceIsRelease = computed(() => pageParams.value?.source === 'release')

// 添加地址
const goAddressPage = () => {
  uni.navigateTo({
    url: '/sub_business/pages/AddressCenter/AddressAdd',
  })
}
// 编辑
const edit = (item) => {
  const str = JSON.stringify(item)
  uni.navigateTo({
    url: `/sub_business/pages/AddressCenter/AddressAdd?item=${encodeURIComponent(str)}&isAdd=edit`,
  })
}
function handleActiveAddress(item: AnyObject) {
  if (sourceIsRelease.value) {
    releaseActiveAddress.value = item
    uni.navigateBack()
  }
}
// s删除
const delet = async (id) => {
  message
    .confirm({
      title: '提示',
      msg: '您确定要删除吗?',
    })
    .then(() => {
      deleteAdress({ id }).then((res: any) => {
        if (res.code === 0) {
          pagingRef.value.reload()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
            duration: 3000,
          })
        }
      })
    })
}
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res = await queryListAdress({
    entity: {},
    orderBy: {},
    page: pageInfo.page,
    size: pageInfo.size,
  })
  console.log(res, 'res=============')
  if (res.code === 0) {
    pagingRef.value.complete(res.data.list)
  }
}

onShow(async (res) => {
  await uni.$onLaunched
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.btn-fixed {
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 25rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
