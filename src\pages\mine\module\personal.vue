<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar>
        <template #left>
          <view class="title"></view>
        </template>
        <template #right>
          <view class="start_ICON" @click="handPopup">
            <image src="/static/my/shezhi_1.png" mode="aspectFill"></image>
          </view>
        </template>
      </CustomNavBar>
    </template>
    <view class="box" @click="goPersonalInfo">
      <view class="my_flex w-100" :class="myObj.sex === 1 ? 'border-boy' : 'border-griy'">
        <view class="box_colown" style="width: 100%">
          <view
            class=""
            style="
              display: flex;
              flex-direction: row;
              align-items: center;
              justify-content: space-between;
              width: 100%;
            "
          >
            <view class="my_flex_left relative">
              <image class="my_flex_left_icon" :src="myObj.headImgUrl" mode="aspectFill"></image>

              <view class="flex-c" style="padding-top: 26rpx">
                <view class="my_flex_font">{{ myObj.trueName }}</view>
                <!-- <view
                  class="my_flex_font-img"
                  :class="myObj.sex === 1 ? 'my_flex_font-img-1' : 'my_flex_font-img-2'"
                ></view> -->
              </view>
            </view>
            <view class="my_flex_right">
              <view class="my_flex_right_year" v-if="qualification">
                <image
                  class="my_flex_right_year_icon"
                  src="/static/my/Group_55.png"
                  mode="aspectFill"
                ></image>
                <view class="my_flex_right_work">
                  <text v-if="myObj?.school">{{ truncateText(myObj.school, 5) }}</text>
                  {{ truncateText(qualification, 4) }}
                </view>
              </view>
              <view class="my_flex_right_year" @click.stop="goEdtion" v-else>
                <image
                  class="my_flex_right_year_icon"
                  src="/static/my/Group_55.png"
                  mode="aspectFill"
                ></image>

                <view class="my_flex_right_work c-#4d8fff text-26rpx" @click.stop="goEdtion">
                  未完善教育经历
                </view>
              </view>
              <view class="my_flex_content">
                <view class="flex-c">
                  <view v-if="myObj?.age" class="my_flex_year flex items-center">
                    <wd-img :width="14" :height="14" :src="birthdayIcon" class="wd-img" />
                    <text class="c-#333 text-26rpx p-l-10rpx">{{ myObj.age }}岁</text>
                  </view>

                  <!-- <view style="padding: 0rpx 0rpx" v-if="myObj.workAge">·</view> -->
                  <view v-if="myObj?.workAge" class="my_flex_year flex items-center">
                    <wd-img :width="14" :height="14" :src="icons" class="wd-img" />
                    <text class="c-#333 text-26rpx p-l-10rpx">
                      {{
                        myObj?.workAge > 0 && myObj?.workAge <= 10
                          ? myObj.workAge + '年'
                          : myObj.workAge > 10
                            ? '10年以上'
                            : ''
                      }}
                    </text>
                  </view>
                </view>

                <view class="my_flex_year flex items-center">
                  <image
                    class="onlineRes-connect-img"
                    src="@/resumeRelated/img/Group_1171274957.png"
                  ></image>
                  <view>{{ myObj.phone }}</view>
                </view>
                <view class="my_flex_year">
                  {{
                    myObj.seekStatus === 0
                      ? '离职-随时到岗'
                      : myObj.seekStatus === 0
                        ? '在职-等待机会'
                        : '暂不找工作'
                  }}
                </view>
              </view>
            </view>
          </view>
          <!-- <view class="border_flex">
            <view class="border_one">大专</view>
            <view class="border_one">
              <view class="flex_row">
                <image
                  style="width: 20rpx; height: 20rpx; padding-right: 10rpx"
                  src="/static/my/Group_1171274971.png"
                  mode="aspectFill"
                ></image>
                <view class="font_color">教师资格证书</view>
              </view>
            </view>

            <view class="border_one">
              <view class="flex_row">
                <image
                  style="width: 20rpx; height: 20rpx; padding-right: 10rpx"
                  src="/static/my/Group_1171274971.png"
                  mode="aspectFill"
                ></image>
                <view class="font_color">机床资格证书</view>
              </view>
            </view>
          </view> -->
        </view>
      </view>
    </view>
    <view class="list_box">
      <view
        @click="goCollect(index)"
        :class="index == 0 ? 'list_flex_for_1' : 'list_flex_for'"
        v-for="(item, index) in listArray"
        :key="index"
      >
        <view class="list_flex_border">
          <view class="list_flex_font">
            {{ item.name }}
          </view>
          <view class="list_flex_number">
            {{ item.number }}
          </view>
        </view>
      </view>
    </view>
    <view class="list_xian"></view>
    <view class="end_list">
      <view class="end_flex" v-for="(item, index) in dataList" :key="index" @click="goPage(index)">
        <image :src="item.image" mode="aspectFill"></image>
        <view class="end_flex_name">
          {{ item.name }}
        </view>
        <view class="end_flex_des">
          {{ item.des }}
        </view>
      </view>
    </view>
    <view class="end_image_box">
      <image class="end_image" src="/static/my/Rectangle_429.png" mode="widthFix" />
    </view>
    <template #bottom>
      <view class="m-b-20rpx">
        <CommonLink></CommonLink>
      </view>
      <customTabbar name="mine" />
    </template>
  </z-paging>
</template>

<script setup>
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import CommonLink from '@/components/CommonLink/CommonLinkMy.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import { cuserInfo } from '@/interPost/resume'
import { truncateText } from '@/utils/util'
import { myMessageShort } from '@/interPost/home'
import icons from '@/resumeRelated/img/icons.png'
import birthdayIcon from '@/resumeRelated/img/birthday_icon.png'
defineOptions({
  name: 'MinePersonal',
})

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 学历
const qualification = ref(null)
const myObj = ref({})
const dataList = ref([
  {
    image: '/static/my/Group_1171275001.png',
    name: '在线简历',
    des: '优化增加推荐',
  },
  {
    image: '/static/my/Group_1171275000.png',
    name: '求职意向',
    des: '离职-随时到岗',
  },
  {
    image: '/static/my/Mask_group.png',
    name: '附件简历',
    des: '简历-附件上传',
  },
  {
    image: '/static/my/Group_1171274998.png',
    name: '高薪职业',
    des: '高薪-智能匹配',
  },

  {
    image: '/static/my/Group_1171275033(1).png',
    name: '消息通知',
    des: '站内消息通知',
  },
  {
    image: '/static/my/<EMAIL>',
    name: '违规公示',
    des: '违规-账号查询',
  },
  {
    image: '/static/my/Group_1171275034.png',
    name: '协议条例',
    des: '协议-查看协议',
  },
  {
    image: '/static/my/Group_1171274999.png',
    name: '投递问题',
    des: 'AI-智能回复',
  },
])
const listArray = ref([
  {
    name: '沟通过',
    number: 0,
  },
  {
    name: '已投简历',
    number: 0,
  },
  {
    name: '待面试',
    number: 0,
  },
  {
    name: '收藏',
    number: 0,
  },
])
// 我的列表
const myList = async () => {
  const res = await myMessageShort()
  if (res.code === 0) {
    myObj.value = res.data
    if (res.data.headImgUrl) {
      myObj.value.headImgUrl = res.data.headImgUrl
    } else {
      myObj.value.headImgUrl =
        myObj.value.sex === 1 ? '/static/header/jobhunting1.png' : '/static/header/jobhunting2.png'
    }
    if (res.data?.qualification) {
      qualification.value =
        res.data?.qualification === 1
          ? '高中及以上'
          : res.data?.qualification === 2
            ? '专科'
            : res.data?.qualification === 3
              ? '本科'
              : res.data?.qualification === 4
                ? '硕士'
                : res.data?.qualification === 5
                  ? '博士及以上'
                  : ''
    }
  }
}
const goEdtion = () => {
  uni.navigateTo({
    url: '/resumeRelated/onlineResume/index',
  })
}
// 获取简历沟通
const getList = async () => {
  const res = await cuserInfo()
  console.log(res, 'res===')
  if (res.code === 0) {
    listArray.value[0].number = res.data.interactCount
    listArray.value[1].number = res.data.sendResumeCount
    listArray.value[2].number = res.data.interviewTodoCount
    listArray.value[3].number = res.data.collectCount
  }
}

const goPersonalInfo = () => {
  uni.navigateTo({
    url: '/setting/personalInfo/index',
  })
}

const handPopup = () => {
  uni.navigateTo({
    url: '/setting/setting/index',
  })
}

const goCollect = (index) => {
  if (index === 0) {
    uni.navigateTo({
      url: '/resumeRelated/communicate/index?indexActive=' + 1,
    })
  }
  if (index === 1) {
    uni.navigateTo({
      url: '/resumeRelated/communicate/index?indexActive=' + 2,
    })
  }
  if (index === 2) {
    uni.navigateTo({
      url: '/resumeRelated/interview/index',
    })
  }
  if (index === 3) {
    uni.navigateTo({
      url: '/resumeRelated/collect/index',
    })
  }
}

const goPage = (index) => {
  console.log(index, 'index')
  if (index === 0) {
    uni.navigateTo({
      url: '/resumeRelated/onlineResume/index',
    })
  }
  if (index === 1) {
    uni.navigateTo({
      url: '/resumeRelated/jobExpectations/index',
    })
  }
  if (index === 2) {
    uni.navigateTo({
      url: '/resumeRelated/AttachmentResume/index',
    })
  }
  if (index === 3) {
    uni.navigateTo({
      url: '/resumeRelated/salaryWork/index',
    })
  }
  if (index === 4) {
    uni.navigateTo({
      url: '/chartPage/message/index',
    })
  }
  if (index === 5) {
    uni.navigateTo({
      url: '/resumeRelated/violationDis/index',
    })
  }
  if (index === 6) {
    uni.navigateTo({
      url: '/setting/PrivacyAgreement/index',
    })
  }
  if (index === 7) {
    uni.navigateTo({
      url: '/resumeRelated/CustomerService/index',
    })
  }
}

const mineShow = () => {
  getList()
  myList()
}

defineExpose({
  mineShow,
})
</script>

<style lang="scss" scoped>
.end_image_box {
  box-sizing: border-box;
  width: 100%;
  padding: 40rpx;

  image {
    width: 100%;
    height: 400rpx;
  }
}
.wd-img {
  display: flex;
  flex-shrink: 0; // 防止图标被压缩
  align-items: center;
  color: #333;
}
.border-boy {
  border: 3rpx solid #3e9cff;
}
.border-griy {
  border: 3rpx solid rgba(255, 190, 190, 1);
}
.border_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  width: 100%;
  padding-top: 20rpx;

  .border_one {
    padding: 10rpx 20rpx;
    margin-right: 10rpx;
    margin-bottom: 10rpx;
    font-size: 22rpx;
    font-weight: 400;
    line-height: 20rpx;
    color: #4075ff;
    background: #d7dfff;
    border-radius: 26rpx 26rpx 26rpx 26rpx;
  }
}

.font_color {
  font-size: 22rpx;
  font-weight: 400;
  line-height: 20rpx;
  color: #4075ff;
}

.list_xian {
  width: 100%;
  height: 10rpx;
  background: #efefef;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
}

.navigation-bar {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 999;
}

.box_colown {
  display: flex;
  flex-direction: column;
}

.start_ICON {
  image {
    width: 56rpx;
    height: 56rpx;
  }
}

.navigation-bar-betwween {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 40rpx;
}

.title {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 60rpx;
  font-weight: 500;
  line-height: 28rpx;
  color: #000000;

  image {
    width: 56rpx;
    height: 56rpx;
  }
}

.box {
  box-sizing: border-box;
  width: 100%;
  padding: 0rpx 80rpx 0rpx;
}

.list_flex_font {
  font-size: 24rpx;
  font-weight: 400;
  line-height: 30rpx;
  color: #888888;
  white-space: nowrap;
}

.list_flex_number {
  padding-top: 20rpx;
  font-size: 40rpx;
  font-weight: 500;
  line-height: 20rpx;
  color: #000000;
}

.end_list {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;
  padding: 60rpx 40rpx 0rpx;

  .end_flex {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 25%;
    padding-bottom: 40rpx;

    image {
      width: 50rpx;
      height: 50rpx;
    }

    .end_flex_name {
      padding-top: 16rpx;
      padding-bottom: 10rpx;
      font-size: 24rpx;
      font-weight: 400;
      line-height: 20rpx;
      color: #333333;
    }

    .end_flex_des {
      font-size: 20rpx;
      font-weight: 400;
      line-height: 20rpx;
      color: #888888;
    }
  }
}

.list_box {
  box-sizing: border-box;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 50rpx 40rpx 50rpx;
  .list_flex_for_1 {
    padding: 0 40rpx;

    .list_flex_border {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  .list_flex_for {
    padding: 0 40rpx;
    border-left: 1rpx solid #d2d2d2;

    .list_flex_border {
      display: flex;
      flex-direction: column;
      align-items: center;

      .list_flex_font {
        white-space: nowrap;
      }
    }
  }
}

.my_flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 50rpx;
  background: #ffffff;
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

  .my_flex_right {
    display: flex;
    flex-direction: column;

    .my_flex_year {
      margin-right: 10rpx;
      font-size: 26rpx;
      font-weight: 400;
      line-height: 44rpx;
      color: #333333;
    }
    .onlineRes-connect-img {
      width: 20rpx;
      height: 24rpx !important;
      padding: 0 10rpx 0 0;
    }

    .my_flex_content {
      width: 300rpx;
      padding: 10rpx 16rpx;
      margin-top: 10rpx;
      background: #f8faff;
      border-radius: 36rpx 36rpx 36rpx 36rpx;

      // image {
      // 	width: 32rpx;
      // 	height: 32rpx;
      // }
    }

    .my_flex_right_year {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 300rpx;
      padding: 5rpx 16rpx;
      background: #f8faff;
      border-radius: 36rpx 36rpx 36rpx 36rpx;
      &_icon {
        width: 42rpx;
        height: 42rpx;
      }

      .my_flex_right_work {
        padding-left: 10rpx;
        font-size: 22rpx;
        font-weight: 400;
        color: #000;
      }
    }
  }

  .my_flex_left {
    display: flex;
    flex-direction: column;
    align-items: center;

    &_icon {
      width: 184rpx;
      height: 184rpx;
      border-radius: 50%;
    }

    .my_flex_font {
      font-size: 34rpx;
      font-weight: 600;
      line-height: 20rpx;
      color: #000000;
    }

    .my_flex_font-img {
      width: 40rpx;
      height: 40rpx;

      background-size: 100% 100%;
    }
    .my_flex_font-img-1 {
      background-image: url('@/static/my/nan_1.png');
    }
    .my_flex_font-img-2 {
      width: 35rpx;
      height: 35rpx;
      background-image: url('@/resumeRelated/img/sex.png');
    }
  }
}

.flex_row {
  display: flex;
  flex-direction: row;
  align-items: center;
}
</style>
