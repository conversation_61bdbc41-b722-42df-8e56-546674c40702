<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘简历用户协议</text>
      </view>
      <view class="protocol-meta">
        <text class="version">版本：ver202505</text>
        <text class="date">生效日期：2025年05月24日</text>
      </view>
      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <!-- <text class="list-icon"></text> -->
          <text class="list-text">{{ item }}</text>
        </view>
      </view>

      <text class="welcome-text">
        尊敬的用户，欢迎您使用易直聘简历服务！\n本协议由您与重庆中誉易职网络信息科技有限公司（以下简称"我们"）就使用易直聘简历服务签订的合法文件。为保障用户权益并提供优质服务体验，在使用相关功能前，请务必仔细阅读《易直聘简历用户协议》（下称"本协议"）。用户完成易直聘简历的注册或登录操作，即代表您已确认并同意遵守本协议的全部条款。
      </text>

      <view
        class="section"
        v-for="(section, index) in sections"
        :key="'section' + index"
        :id="'section-' + index"
      >
        <view class="section-title article-title">{{ section.title }}</view>
        <text class="section-content">{{ section.content }}</text>
      </view>

      <view class="footer">
        <text class="footer-text">重庆中誉易职网络信息科技有限公司</text>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const contentList = ref([
  '一、用户的注册与登录',
  '二、服务说明',
  '三、用户的使用义务',
  '四、知识产权',
  '五、隐私保护',
  '六、有限责任条款',
  '七、违约责任和违规处置',
  '八、协议变更和通知',
  '九、协议的中止或终止',
  '十、法律适用和管辖',
  '十一、其他',
  '十二、定义',
])

const sections = ref([
  {
    title: '一、用户的注册与登录',
    content: `1. 使用易直聘简历服务前，需先完成账号注册并遵守平台规则。如未注册或账号状态异常，将无法正常登录简历系统。
2.注册及登录过程中需提供本人有效手机号码，平台将通过发送短信验证码的方式验证身份的真实性。
3.用户确认，在开始使用易直聘简历服务前须年满16周岁，且具备与使用行为相匹配的民事行为能力。不符合条件者及其监护人需依法承担相应法律后果。
4. 用户须依据本协议及《易直聘用户协议》妥善管理并维护账号安全。`,
  },
  {
    title: '二、 服务说明',
    content: `1.易直聘简历通过互联网，向用户提供线上及线下相关网络服务。用户需自行准备互联网接入设备（包括计算机或移动设备、调制解调器等终端装置），并承担相关网络接入费用。
2.针对部分网络服务项目，易直聘简历可能收取相应费用，相关页面将进行显著提示。用户拒绝支付费用将无法使用对应服务。付费业务将另行制定专门服务条款规范权利义务关系，用户购买即视为同意该条款内容。
3.所有免费与付费服务均设有固定有效期，有效期届满后服务自动终止且不可暂停或延期。除特别约定外，付费服务费用不予退还。
4.平台保留通过邮件、短信、电话及其他方式向用户发送商业广告及活动信息的权利。用户可通过指定渠道退订非必需的系统通知及重要公告之外的信息推送。
5. 为优化服务体验，经用户授权后平台可将公开信息共享至关联平台及第三方合作平台（具体共享范围详见《易直聘简历隐私政策》）。用户在使用过程中可能查阅到来自其他关联平台的相关内容。`,
  },
  {
    title: '三、用户的使用义务',
    content: `1. 用户在使用易直聘简历服务时，应当严格遵守现行法律法规，禁止借助该平台制作、传播、复制、发布或散布任何违反法律规范的内容，包括但不限于以下情形：
1.1 违背宪法确立的根本原则；
1.2危害国家主权安全，泄露国家机密，破坏政权稳定及国家统一；
1.3 损害国家形象与利益；
1.4 煽动民族对立与歧视，破坏民族团结；
1.5 违反宗教政策，传播邪教及封建迷信思想；
1.6散布不实信息，扰乱社会秩序及稳定；
1.7传播淫秽色情、赌博暴力、恐怖犯罪等违法内容；
1.8 侵害他人名誉权、隐私权等合法权益；
1.9包含虚假欺骗、侵权胁迫、骚扰诽谤等违背公序良俗的信息；
1.10其他违反中国法律法规及具有法律效力规范性文件的内容。

2. 用户承诺在使用本服务过程中，不得实施下列违法违规或损害平台及他人权益的行为：
2.1发布违法信息或损害平台商誉的内容；
2.2 采用非正当手段或违反诚信原则使用本服务；
2.3 出于商业经营目的进行非授权使用；
2.4 未经书面授权进行账号交易、共享或提供自动登录凭证；
2.5 上传侵犯知识产权或隐私权的资料；
2.6 传播计算机病毒等恶意程序；
2.7 干扰平台系统及功能的正常运行；
2.8 非法获取、篡改或使用用户数据；
2.9 违反注册认证规则及使用限制；
2.10 未经许可使用第三方工具接入服务；
2.11 通过非正常手段获取平台数据用于商业用途；
2.12 对平台数据进行逆向工程或超出授权范围使用；
2.13 引发平台与第三方纠纷的行为；
2.14 违反本协议及相关隐私政策的其他行为。
3. 平台对涉嫌违规内容具有处置权，包括但不限于修改、删除相关信息，暂停或终止部分及全部服务功能。`,
  },
  {
    title: '四、知识产权',
    content: `1. 您通过易直聘简历上传或填写的所有信息均须为原创作品或已取得合法授权（包括转授权），且不得侵犯任何第三方的合法权益，相关内容的著作权归您或原始权利人所有。如第三方提出知识产权异议，我们有权直接删除争议内容并追究相关责任。若因此造成我方或第三方损失，您需承担全部赔偿义务。
2. 完成易直聘简历注册后，除特别约定外，您确认将平台上传/发布的所有内容在全球范围内永久、不可撤销、非独家地授予我们及关联企业信息网络传播权、展览权、翻译权、复制权及其再许可权。基于此项授权，我们有权在平台进行内容展示及推广。
3. 使用易直聘简历服务时，您将获得非独占、不可转授、可撤销的使用许可，仅限于求职目的使用平台简历模板的版式设计、文字内容及内置素材。
4. 易直聘简历相关知识产权（包含但不限于软件技术、网页设计、商标标识、排版方案等）均归属我方所有。未经书面许可，任何机构或个人不得擅自使用、展示、注册或暗示具备相关权利，违者需承担由此产生的全部法律责任。
5. 如发现他人存在违反本协议或侵害知识产权的行为，您可通过平台举报通道反映，我们将依据规则采取相应处理措施。`,
  },
  {
    title: '五、隐私保护',
    content:
      '我们严格依照相关法律法规对用户的个人信息及隐私数据进行保护，具体内容请参考《易直聘简历隐私政策》中的相关规定。',
  },
  {
    title: '六、有限责任条款',
    content: `1. 易直聘简历承诺为用户提供安全、及时、准确的高质量服务，但不对服务效果、时效性、安全性及准确性作任何保证，亦不承诺服务永不中断。除另有约定外，用户因服务不可用或未达预期而产生的损失，本公司不承担相关责任。
2. 平台展示的第三方广告信息、链接及资讯等内容（如有），其准确性、合法性或可靠性由广告主负责。用户与广告主之间产生的商业往来及关联行为均属独立民事关系，与易直聘简历无涉，由此引发的任何损害或损失，本公司不承担法律责任。
3. 用户上传的证件、影像、图文资料等内容，虽经平台采取必要审核措施并尽合理注意义务，仍无法确保其准确性、合法性及可靠性，相关责任由内容提供方自行承担。
4. 用户须自行判断并承担使用本平台所获信息资料的风险，包括但不限于对内容正确性、完整性或实用性的误判风险。对于因依赖平台信息导致的任何直接或间接损失，本公司依法不承担责任。
5. 用户下载或获取任何资料应自担风险，因下载行为导致的设备损坏、数据丢失等后果，本公司不承担技术及赔偿责任。
6. 用户知悉并同意，因不可抗力（含政府行为、自然灾害、网络故障、黑客攻击、战争等）导致的服务中断或缺陷，我们将在能力范围内尽力减少损失，但依法免除相关责任，且不保证服务的持续性、安全性及绝对准确性。
7. 平台保留对产品功能、服务内容及界面设计进行调整优化的权利。用户承诺对因合理调整造成的服务波动予以充分理解，我们将最大程度降低调整对用户体验的影响。
8. 下列情形导致服务中止或终止的，本公司不承担法律责任：
8.1 实施系统维护、服务器迁移等必要技术操作导致的临时性服务中断；
8.2 互联网通讯服务商故障引发的网络接入异常；
8.3 遭遇非法网络攻击造成的服务器运行异常；
8.4 因网络特性导致的区域性临时访问障碍，包括网络拥塞、短暂中断等现象。`,
  },
  {
    title: '七、违约责任和违规处置',
    content: `1.若您存在违反本协议或相关法律法规的情形，我们有权根据独立判断随时采取包括但不限于警告、功能限制、账户冻结、服务终止及永久封禁等措施，并可能对违规内容实施删除、屏蔽、停止传播或断开链接等处理。您明确知悉并同意，被删除内容将无法恢复，相关责任及后果均由您自行承担。
2.您充分理解我们有权公示处理决定，保留恢复账户功能的裁量权，并有权拒绝向违规主体继续提供服务。对于涉嫌违法或犯罪的行为，我们依法保留采取证据保全措施的权利，并将根据法律要求向监管部门报告或配合司法机关调查。
3.若因您违反协议或侵害第三方权益导致投诉或诉讼，我们将在收到有效权利主张后对涉嫌违规内容进行删除处理。您须自行应对所有权利主张并承担相应法律责任。因您的不当行为致使我们承担赔偿或受到行政处罚的，您应全额补偿我们因此产生的全部损失（含经济损失、第三方索赔、商誉损害及维权支出等）。`,
  },
  {
    title: '八、协议变更和通知',
    content:
      '根据产品功能升级、运营调整等需要，我方保留随时对本协议条款进行更新或调整的权利。协议条款变更后，我们将通过易直聘平台内更新提示、系统通知或页面公告等途径发布新版《用户协议》。若您对条款修改存有异议，应当立即停止使用平台服务，并提出数据资料删除请求或账号注销申请；选择注销账号的用户，其未消耗的付费权益将同步清零。若您继续使用平台服务，则视为接受修订后的协议条款。',
  },
  {
    title: '九、协议的中止或终止',
    content: `在以下情形中，我们可能中止或终止向您提供服务：
1. 根据相关法律法规、政策规定或监管要求，我们可能暂停或停止相关服务；
2. 因业务调整需要，我们将通过提前发布公告等形式通知服务的暂停或终止；
3. 若您存在违反法律法规或协议约定的行为，我们有权按照协议规定暂停或终止服务。`,
  },
  {
    title: '十、法律适用和管辖',
    content: `1. 本协议的生效条件、履行方式、条款解释及争议处理均适用中华人民共和国现行有效之法律。
2. 本协议签订地点为中华人民共和国北京市朝阳区。若因本协议引发争议，双方应优先通过友好协商解决；若协商未果，任一方均有权向北京市朝阳区人民法院提起诉讼。`,
  },
  {
    title: '十一、其他',
    content: `1. 本协议条款标题仅为便于阅读而设置，不可作为条款释义的依据。
2. 本协议与《易直聘用户协议》共同组成法律文件，作为您与平台合作的规范依据。
3. 若本协议条款与中华人民共和国法律存在冲突导致部分条款无效或无法执行，相关条款应在法律允许范围内进行最大程度的有效性解释，且不影响其他条款效力，缔约双方应继续履行其他条款义务。`,
  },
  {
    title: '十二、定义',
    content: `1. "用户"或"您"特指通过注册流程使用重庆中誉易职网络信息科技有限公司运营产品及服务的自然人主体。
2. "易直聘简历"涵盖易直聘简历网页平台及其微信小程序应用服务模块。
3. "易直聘平台"指由重庆中誉易职网络信息科技有限公司及其关联企业运营的网页、移动应用、小程序。
4. "易直聘账号"系指能够登录并操作易直聘平台服务体系的通用账号系统。`,
  },
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  console.log('点击定位到第', index, '个章节')

  const sectionId = `section-${index}`
  console.log('目标章节ID:', sectionId)

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      console.log('目标元素位置:', targetRect)

      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop
            console.log('计算滚动位置:', targetScrollTop)

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {
                    console.log('uni.pageScrollTo 成功')
                  },
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.protocol-meta {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.welcome-text {
  display: block;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}
.section-content {
  display: block;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}
.version,
.date {
  font-size: 24rpx;
  color: #666;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.agreement-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx;
  margin-bottom: 120rpx;

  .header {
    margin-bottom: 20rpx;
    text-align: center;

    .title {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }

    .version,
    .date {
      display: block;
      font-size: 24rpx;
      line-height: 1.6;
      color: #999;
    }
  }

  .content-list {
    padding: 20rpx 0rpx;
    margin-bottom: 20rpx;
    border-radius: 12rpx;

    .content-title {
      display: block;
      margin-bottom: 15rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }

    .list-item {
      display: flex;
      align-items: flex-start;
      padding: 10rpx;
      margin-bottom: 10rpx;
      cursor: pointer;
      border-radius: 8rpx;

      .list-icon {
        margin-right: 10rpx;
        font-size: 24rpx;
      }

      .list-text {
        flex: 1;
        font-size: 26rpx;
        color: #007aff;
      }
    }
  }

  .content {
    flex: 1;
    padding: 20rpx;
    margin-bottom: 120rpx;
    border-radius: 12rpx;

    .section {
      margin-bottom: 30rpx;

      .section-title {
        display: block;
        margin-bottom: 15rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .footer {
      margin-top: 40rpx;
      text-align: center;

      .footer-text {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .action-bar {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100rpx;
    padding: 0 20rpx;

    .btn {
      width: 45%;
      height: 80rpx;
      margin: 0;
      font-size: 28rpx;
      line-height: 80rpx;
      border-radius: 40rpx;

      &.disagree {
        color: #666;
      }

      &.agree {
        color: #fff;
      }
    }
  }
}
</style>
