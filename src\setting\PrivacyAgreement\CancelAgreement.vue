<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="container">
      <view class="header">
        <text class="title">易直聘注销协议</text>
      </view>

      <!-- 目录导航 -->
      <view class="directory">
        <view class="directory-list">
          <view class="directory-item" @click="scrollToSection(0)">一、通知</view>
          <view class="directory-item" @click="scrollToSection(1)">二、特别提示</view>
          <view class="directory-item" @click="scrollToSection(2)">
            三、为了确保您的账户安全和财产利益，在您请求注销账户之前，您需要确认该账户处于正常状态，否则，您可能无法进行注销。"正常状态"意味着该账户必须满足以下所有条件
          </view>
          <view class="directory-item" @click="scrollToSection(3)">
            四、您明白并接受，一旦注销账号，以下后果将由您个人承担
          </view>
          <view class="directory-item" @click="scrollToSection(4)">五、账号注销流程</view>
        </view>
      </view>

      <view class="section" id="section-0">
        <text class="section-title">一、通知</text>
        <view class="section-content">
          <text>
            1.重庆中誉易职网络信息技术有限公司（以下简称"我们"）管理的本平台账号所关联的手机号码，是您在使用我们的平台服务（包括易直聘网页，移动应用，小程序）时所使用的通用登录账号。在您考虑申请账号注销之前，请务必仔细考虑注销可能存在的风险和损失。
          </text>
          <text>
            2.如果您选择"注销易直聘"，那么注销后您将失去对易直聘及其相关平台的访问权限；一旦确认注销，您将被视为同意放弃您所选择注销范围内对应平台的所有财产权益，并且同意我们将您的信息彻底清除，一旦清除，信息将无法恢复。我们强烈建议您在确认注销前，登录相关平台页面仔细检查您的财产权益详情。
          </text>
          <text>
            3.特别需要指出的是，本文中的"账号"指的是您的个人账号，您可以自行决定注销个人账号内的部分或全部产品与服务；"产品与服务"指的是您申请注销时所涉及的我们提供的产品与服务，本《注销协议》的条款解释将依据您实际选择注销的具体范围来确定。
          </text>
        </view>
      </view>

      <view class="section" id="section-1">
        <text class="section-title">二、特别提示</text>
        <view class="section-content">
          <text>
            1.当您按照注销页面提示填写信息、阅读并同意本《注销协议》及相关条款与条件且完成全部注销程序后，即表示您
            已充分阅读、理解并接受本《注销协议》 的全部内容。阅读本《注销协议》
            的过程中，如果您不同意相关任何条款和条件约定，请您立即停止账号注销程序。
          </text>
          <text>
            2.账号注销意味着我们将在系统中删除您的个人信息，使其保持不可被检索、访问的状态，或对其进行匿名化处理。账号注销后，您将无法再以此账号登录和使用所对应的产品与服务以及其中与第三方合作的服务内容。我们在此善意地提醒您，您注销账户的行为会给您向我们带来诸多不便。账号一旦注销完成，将无法恢复，请您在注销前慎重考虑。
          </text>
          <text>
            3.出于账号安全的考量，我们可能会对申请账号注销的用户执行身份验证程序，具体验证方法将依据页面展示的信息。
          </text>
          <text>
            4.当您选择勾选此《注销协议》并继续下一步时，即表示您已正式签署该协议，并同意完全遵守其所有条款。
          </text>
          <text class="notice">（如您确定注销账号的，请继续向下阅读）</text>
        </view>
      </view>

      <view class="section" id="section-2">
        <text class="section-title">
          三、为了确保您的账户安全和财产利益，在您请求注销账户之前，您需要确认该账户处于正常状态，否则，您可能无法进行注销。"正常状态"意味着该账户必须满足以下所有条件：
        </text>
        <view class="section-content">
          <text>1.此账户必须是您通过我们官方途径注册，并且由您亲自使用的；</text>
          <text>2.此账户内没有提供服务但未结算的订单；</text>
          <text>
            3.此账户不存在任何未解决的投诉、争议或纠纷，包括但不限于账户被冻结后未解封或恢复、投诉他人或被他人投诉举报以及升级认证未完成的情况；
          </text>
          <text>4.此账户在最近三个月内没有被检测到异常登录或其他不安全、异常行为的记录；</text>
          <text>5.此账户没有任何未解决的违规行为导致的限制、冻结或其他处罚记录。</text>
          <text>
            6.当您提交注销申请后，如果发现或被告知该账户涉及争议、纠纷等，包括但不限于投诉、举报、诉讼、仲裁、国家权力机关或监管部门的调查等，我们有权暂停处理您的注销申请，并且无需另行征得您的同意。
          </text>
        </view>
      </view>

      <view class="section" id="section-3">
        <text class="section-title">四、您明白并接受，一旦注销账号，以下后果将由您个人承担：</text>
        <view class="section-content">
          <text>1.您将失去登录该账号的能力，无法继续利用此账号享受相关产品和服务。</text>
          <text>
            2.您将无法恢复或查看账号内的所有数据和信息，这包括但不限于用户名/昵称、头像、上传的简历、在线简历、系统通知、发布的职位信息、内部消息、历史订单详情、使用历史等。
          </text>
          <text>3.您将无法重新获取该账号在PC端、手机App等设备上的所有使用记录。</text>
          <text>
            4.若您决定注销账户，您在使用期间累积的、尚未用尽的增值服务、财产权益以及任何预期收益将被视为您已主动放弃，无法再继续享用。我们将对您账户相关的所有权益执行清除操作，这包括但不限于E币（易直聘app中提供的数字化商品，以下统一简称"E币"）、未使用的付费职位、虚拟物品、红包、优惠券、可退还的E币、以及正在申请退款的各类财产权益（统称为"剩余资产"）。在您提出注销请求时，如果账户内有剩余资产，我们会提醒您，以便您根据平台指引妥善处理这些资产。另外，若您需要对已支付订单开具发票，请在注销账户前依照平台流程和发票开具规定进行申请。
          </text>
          <text>
            5.一旦该账户被注销，它将无法被恢复。在您注销账户后，即便使用相同的手机号码或身份证号码重新注册并审核成功，新账户也将被视为一个全新的用户账户。
          </text>
          <text>
            6.请留意，一旦您的账号被注销，它将无法被恢复。因此，在执行此操作前，我们建议您备份所有与账号相关的资料和数据。
          </text>
        </view>
      </view>

      <view class="section" id="section-4">
        <text class="section-title">五、账号注销流程</text>
        <view class="section-content">
          <text>
            1.请依照官方渠道提供的注销流程和操作指南，在功能设置页面执行注销步骤，根据您的实际意愿选择账号注销范围，例如"注销易直聘"；
          </text>
          <text>
            2.您已经详尽阅读并完全理解并同意本《注销协议》的全部条款；同时，您已经确认您申请注销的账号已达到本协议第一条的标准；
          </text>
          <text>
            3.在您完成上述第2点后，我们将根据您申请注销的账号的安全状况以及您使用相关产品与服务的情况等综合评估您的账号是否满足注销条件；
          </text>
          <text>
            4.若满足注销条件，为确保您的账号和财产权益的安全，我们将执行注销申请的身份验证（例如注册手机验证等），以确认您是申请注销账号的合法持有者；
          </text>
          <text>
            5.若我们在综合评估后发现您申请注销的账号不满足注销条件，或者"身份验证"未通过，您的注销可能会受阻。您可以依照我们的注销流程继续后续操作或联系客服寻求帮助；
          </text>
          <text>
            6.在您完成前述"身份验证"后，意味着您已向我们正式提交了账号注销申请。在此期间，我们为您申请注销的账号提供7个自然日的冷静期（即"冻结期"）。在此冻结期内，您无法登录和使用相关账号对应的产品与服务，且我们会将您申请注销账号范围内发布的相关信息从业务场景中移除，任何人都无法通过我们的服务查看到您的个人信息，但前述信息暂时不会进行物理性删除或匿名化处理。冻结期内，您在提供相关身份信息完成验证后，可以通过客服或我们公布的其他途径申请恢复该账号；
          </text>
          <text>
            7.如冻结期结束，我们未收到您恢复账号的请求或者未出现任何其他不得注销的情况，您申请注销的账号将被永久注销。（具体的注销流程和操作以我们在官方渠道公布或产品页面提示的为准。）
          </text>
          <text>
            8.您明白并同意，在您申请注销账号期间，如我们需要对相关交易、投诉或其他与该账号相关的事项进行核查，或者有其他合理原因，我们有权暂时冻结您申请注销的账号且暂时不提供注销服务。
          </text>
          <text>
            9.本协议如有未尽事宜，将适用易直聘《用户协议》《隐私政策》或其他相关平台规则、说明、提示等。
          </text>
          <text>10.若您在注销过程中遇到任何问题，可联系我们的客服协助解决。</text>
          <text>11.您取消账户的举动，并不能自动地减轻或解除您应负的法律或约定的职责或义务。</text>
          <text>12.当您的账户被注销之后，我们有权利不回应您提出的任何与之前相关的请求。</text>
        </view>
      </view>

      <view class="footer">
        <text>生效日期：2025年05月24日</text>
        <text>版本：ver202505</text>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>
<style>
.container {
  padding: 20rpx 40rpx;
  padding-bottom: 120rpx;
}

.header {
  margin-bottom: 30rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  text-align: center;
}

.content-list {
  /* padding: 20rpx; */
  margin-bottom: 40rpx;
  border-radius: 10rpx;
}

.list-title {
  display: block;
  margin-bottom: 10rpx;
  font-weight: bold;
}

.list-item {
  display: block;
  padding: 10rpx;
  margin-bottom: 10rpx;
  /* margin-left: 20rpx; */
  line-height: 1.6;
  color: #007aff;
  cursor: pointer;
  border-radius: 8rpx;
  transition: background-color 0.2s ease;
}

.list-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.list-item:active {
  background-color: rgba(0, 0, 0, 0.1);
}

.directory {
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.directory-item {
  flex: 1;
  padding-bottom: 20rpx;
  font-size: 26rpx;
  color: #007aff;
}

.directory-item text {
  flex: 1;
  font-size: 26rpx;
  color: #007aff;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-weight: bold;
  color: #333;
}

.section-content {
  line-height: 1.8;
}

.section-content text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 28rpx;
  color: #333;
  text-align: justify;
}

.notice {
  margin-top: 20rpx;
  font-weight: bold;
  color: #f56c6c;
}

.footer {
  margin-top: 40rpx;
  font-size: 24rpx;
  color: #999;
  text-align: right;
}

.footer text {
  display: block;
}

.action-buttons {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  padding: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  margin: 0 10rpx;
  font-size: 32rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.cancel {
  color: #666;
}

.confirm {
  color: #fff;
}
</style>
