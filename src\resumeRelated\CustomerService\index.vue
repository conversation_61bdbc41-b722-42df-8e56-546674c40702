<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="客服小易"></CustomNavBar>
    </template>
    <view class="pageContaner relative">
      <view class="absolute top-10rpx">
        <wd-img :src="kf" width="112" height="122" />
      </view>
      <view class="absolute top-60rpx left-260rpx">
        <view class="c-000 text-36rpx font-fameliy">Hi～，有什么可以帮您！</view>
        <view class="c-#555 text-28rpx">智能小易为您服务</view>
      </view>
      <view class="p-t-200rpx">
        <view class="pageContaner-card flex items-center">
          <view class="w-25 text-center m-t-40rpx" v-for="(item, index) in listTop" :key="index">
            <wd-img :src="`/static/my/customerService/icon${item.i}.png`" width="30" height="30" />
            <view class="c-#555 text-24rpx">{{ item.name }}</view>
          </view>
        </view>
      </view>
      <view class="m-t-40rpx">
        <view class="pageContaner-botto-card">
          <view class="flex items-center p-b-10rpx border-bottom m-b-10rpx">
            <wd-img :src="`/static/my/customerService/icon1.png`" width="24" height="24" />
            <view class="text-24rpx c-#333 p-l-6rpx">猜您想问</view>
          </view>
          <view class="c-#589BFF text-24rpx line-height-8">如何修改发布职位-职位名称</view>
          <view class="c-#589BFF text-24rpx line-height-8">如何修改发布职位-职位名称</view>
          <view class="c-#589BFF text-24rpx line-height-8">如何修改发布职位-职位名称</view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn_box fixed bottom-80rpx left-40rpx right-40rpx">
        <wd-input
          no-border
          placeholder="发送您想问的问题"
          v-model="keyword"
          confirm-type="send"
        ></wd-input>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import kf from '@/static/my/customerService/kf.png'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const listTop = reactive([
  {
    i: 1,
    name: '认证问题',
  },
  {
    i: 2,
    name: '支付问题',
  },
  {
    i: 3,
    name: '职位问题',
  },
  {
    i: 4,
    name: '沟通问题',
  },
])
const keyword = ref('')
onLoad((options) => {})
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  background-color: transparent !important;
}
::v-deep .wd-input__placeholder {
  font-size: 28rpx !important;
  color: #888888 !important;
}
::v-deep .wd-input__inner {
  font-size: 28rpx !important;
  font-weight: 500;
  color: #888888 !important;
}
.font-fameliy {
  font-family: 'Reem Kufi Fun-SemiBold';
}
.btn_box {
  padding: 20rpx 40rpx;
  background: #e8e8e8;
  border-radius: 30rpx;
}
.pageContaner {
  padding: 0rpx 40rpx 0rpx;
  .pageContaner-card {
    width: 100%;
    height: 200rpx;
    background-image: url('@/static/my/customerService/bg.png');
    background-position: 100% 100%;
    background-size: 100% 100%;
  }

  .pageContaner-botto-card {
    padding: 40rpx 40rpx;
    background-color: #fff;
    border-radius: 30rpx;
    .border-bottom {
      border-bottom: 1rpx solid #ebebeb;
    }
  }
}
</style>
