import { Gender, SeekStatus } from '@/enum'

export interface hrResumeResumeUserSeniorListDataInt {
  /** 年龄起始 */
  ageBegin?: number
  /** 年龄截止 */
  ageEnd?: number
  /** 性别1男2女 */
  gender?: Gender
  /** HR当前所选的职位id */
  positionInfoId: number
  /** 学历等级 */
  qualification?: number
  /** 期望薪资截止(元)，选不限时别送 */
  salaryExpectationEnd?: number
  /** 期望薪资开始(元),选不限时别送 */
  salaryExpectationStart?: number
  /** 查询类型0推荐1最新 */
  searchType?: number
  /** 求职状态0离职找工作1在职找工作2不找工作 */
  seekStatus?: SeekStatus
}
export interface hrResumeSeniorPostModelInt
  extends Omit<hrResumeResumeUserSeniorListDataInt, 'positionInfoId'> {}
