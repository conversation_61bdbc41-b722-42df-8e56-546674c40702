import { CommonUtil } from 'wot-design-uni'
import dayjs, { extend, locale } from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import duration from 'dayjs/plugin/duration'
extend(relativeTime)
extend(isSameOrAfter)
extend(duration)
locale('zh-cn')

/**
 * 获取时间戳
 * @param time 时间
 */
export const currentValueOf = (time?: string) => {
  if (time) {
    return dayjs(time).valueOf()
  }
  return dayjs().valueOf()
}
/**
 * 获取时间差
 */
export const timeDiffValueOf = (
  time: string,
  unit: dayjs.QUnitType | dayjs.OpUnitType = 'seconds',
) => {
  return dayjs().diff(dayjs(time), unit)
}
/**
 * 时间戳格式化
 */
export const valueOfToDate = (val: number, format: string = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(val).format(format)
}
/**
 * 多久之前
 * @param time 时间
 * @param friendly 显示xxx时间前 (显示为几分钟前 几小时前 几天前 几个月前 )
 */
export const getLastTimeStr = (time: string, friendly: boolean = true) => {
  if (friendly) {
    return dayjs(time).fromNow().replace(' ', '')
  }
  return dayjs(new Date(time)).format('YYYY-MM-DD HH:mm')
}
/**
 * 多久之前的时间戳(毫秒)
 * @param val 值
 * @param unit 单位
 */
export const subtractValueOf = (val: number, unit: dayjs.ManipulateType = 'day') => {
  return dayjs().subtract(val, unit).valueOf()
}
/**
 * 多久之后的时间戳(毫秒)
 * @param val 值
 * @param unit 单位
 */
export const addValueOf = (val: number, unit: dayjs.ManipulateType = 'day') => {
  return dayjs().add(val, unit).valueOf()
}
/** 判断是否大于当前时间 */
export const timeIsSameOrAfter = (time: string | number) => {
  if (CommonUtil.isString(time)) {
    return dayjs(time).isSameOrAfter(currentValueOf())
  }
  return dayjs(valueOfToDate(time)).isSameOrAfter(currentValueOf())
}

/**
 * 获取当月月份
 * @returns 当月月份数字（1-12）
 */
export const getCurrentMonth = () => {
  return dayjs().month() + 1 // dayjs的month()返回0-11，需要+1
}

/**
 * 智能时间格式化（优化版）
 * - 当天显示：今天 HH:mm（例如：今天 14:30）
 * - 昨天显示：昨天 HH:mm（例如：昨天 14:30）
 * - 本年内显示：MM月DD日 HH:mm（例如：4月12日 14:30）
 * - 非本年显示：YYYY年MM月DD日 HH:mm（例如：2022年4月12日 14:30）
 * @param time 时间字符串或时间戳
 * @returns 格式化后的时间字符串
 */
export const formatSmartTime = (time: string | number) => {
  if (!time) return '' // 处理空值情况
  // 预处理时间对象
  const date = dayjs(time)
  const now = dayjs()
  // 快速检查无效日期
  if (!date.isValid()) return String(time)
  // 使用dayjs内置方法判断日期关系，比格式化后比较字符串更高效
  if (date.isSame(now, 'day')) {
    // 今天
    return `今天 ${date.format('HH:mm')}`
  } else if (date.isSame(now.subtract(1, 'day'), 'day')) {
    // 昨天
    return `昨天 ${date.format('HH:mm')}`
  } else if (date.isSame(now, 'year')) {
    // 今年
    return date.format('M月D日 HH:mm')
  } else {
    // 往年
    return date.format('YYYY年M月D日 HH:mm')
  }
}
