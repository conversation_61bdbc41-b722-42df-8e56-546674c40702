<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PDF上传 - APP端</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }

      body {
        padding: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: #333;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 600px;
        margin: 0 auto;
      }

      .upload-area {
        padding: 40px 20px;
        margin: 20px 0;
        text-align: center;
        cursor: pointer;
        background: white;
        border: 2px dashed #007aff;
        border-radius: 12px;
        transition: all 0.3s ease;
      }

      .upload-area:hover {
        background-color: #f8f9fa;
        border-color: #0056b3;
      }

      .upload-icon {
        margin-bottom: 20px;
        font-size: 48px;
      }

      .upload-text {
        margin-bottom: 10px;
        font-size: 16px;
        color: #666;
      }

      .upload-hint {
        font-size: 14px;
        color: #999;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        width: 100%;
        padding: 15px 20px;
        margin-top: 20px;
        font-size: 16px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background-color: #007aff;
        border: none;
        border-radius: 8px;
        transition: background-color 0.3s ease;
      }

      .upload-btn:hover {
        background-color: #0056b3;
      }

      .upload-btn:disabled {
        cursor: not-allowed;
        background-color: #6c757d;
      }

      .debug-info {
        max-height: 300px;
        padding: 20px;
        margin-top: 20px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        background: white;
        border-radius: 8px;
      }

      .test-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 20px;
      }

      .test-btn {
        padding: 10px 20px;
        font-size: 14px;
        color: white;
        cursor: pointer;
        background-color: #28a745;
        border: none;
        border-radius: 5px;
      }

      .test-btn.error {
        background-color: #dc3545;
      }

      .test-btn.warning {
        color: #333;
        background-color: #ffc107;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>PDF上传 - APP端专用</h2>

      <div class="upload-area" id="uploadArea">
        <div class="upload-icon">📄</div>
        <div class="upload-text">点击上传PDF文件</div>
        <div class="upload-hint">最大20MB - APP端</div>
        <input type="file" id="fileInput" class="file-input" accept=".pdf,application/pdf" />
      </div>

      <button class="upload-btn" id="uploadBtn" disabled>上传文件</button>

      <div class="test-buttons">
        <button class="test-btn" onclick="testSuccessMessage()">测试成功消息</button>
        <button class="test-btn error" onclick="testErrorMessage()">测试错误消息</button>
        <button class="test-btn warning" onclick="testProgressMessage()">测试进度消息</button>
        <button class="test-btn" onclick="testUniMessage()">测试uni消息</button>
        <button class="test-btn" onclick="testDirectCall()">测试直接调用</button>
      </div>

      <div class="debug-info" id="debugInfo">
        <div>APP端调试信息将显示在这里...</div>
      </div>
    </div>

    <script>
      let selectedFile = null
      let uploadConfig = {
        maxFileSize: 20 * 1024 * 1024,
        allowedTypes: ['application/pdf'],
        uploadUrl: 'https://www.easyzhipin.com/easyzhipin-api/attachment/uploadImgThum',
        headers: {
          token: localStorage.getItem('token') || '',
        },
        timeout: 300000,
      }

      const uploadArea = document.getElementById('uploadArea')
      const fileInput = document.getElementById('fileInput')
      const uploadBtn = document.getElementById('uploadBtn')
      const debugInfo = document.getElementById('debugInfo')

      // 添加调试信息
      function addDebugInfo(message) {
        const timestamp = new Date().toLocaleTimeString()
        const debugLine = document.createElement('div')
        debugLine.innerHTML = `[${timestamp}] ${message}`
        debugInfo.appendChild(debugLine)
        debugInfo.scrollTop = debugInfo.scrollHeight
        console.log(message)
      }

      // APP端专用消息发送函数
      function sendMessageToUniApp(message) {
        addDebugInfo(`发送消息: ${JSON.stringify(message)}`)

        // 直接调用父页面方法（主要方式）
        try {
          if (window.parent && window.parent.handleWebViewMessage) {
            window.parent.handleWebViewMessage({ detail: message })
            addDebugInfo('✓ 直接调用父页面方法成功')
          } else {
            addDebugInfo('✗ 父页面方法不可用')
          }
        } catch (error) {
          addDebugInfo(`✗ 直接调用父页面方法失败: ${error.message}`)
        }

        // APP端 - 使用uni.postMessage（备用方式）
        if (window.uni && window.uni.postMessage) {
          try {
            window.uni.postMessage({
              data: message,
            })
            addDebugInfo('✓ window.uni.postMessage发送成功')
          } catch (error) {
            addDebugInfo(`✗ window.uni.postMessage发送失败: ${error.message}`)
          }
        } else {
          addDebugInfo('✗ window.uni.postMessage不可用')
        }

        // 最后备用方案 - window.parent.postMessage
        if (window.parent) {
          try {
            window.parent.postMessage(message, '*')
            addDebugInfo('✓ window.parent.postMessage发送成功')
          } catch (error) {
            addDebugInfo(`✗ window.parent.postMessage发送失败: ${error.message}`)
          }
        }
      }

      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        addDebugInfo('APP端页面加载完成')

        // 尝试从URL参数获取token
        const urlParams = new URLSearchParams(window.location.search)
        const token = urlParams.get('token') || localStorage.getItem('token') || ''

        if (token) {
          uploadConfig.headers.token = token
          addDebugInfo('从URL参数或localStorage获取到token')
        }

        addDebugInfo(`初始配置: ${JSON.stringify(uploadConfig, null, 2)}`)
        addDebugInfo(`window.uni存在: ${!!window.uni}`)
        addDebugInfo(`window.parent存在: ${!!window.parent}`)

        initEventListeners()
        notifyUniAppReady()
      })

      // 初始化事件监听器
      function initEventListeners() {
        addDebugInfo('初始化事件监听器')

        // 点击上传区域
        uploadArea.addEventListener('click', () => {
          addDebugInfo('上传区域被点击')
          fileInput.click()
        })

        // 文件选择
        fileInput.addEventListener('change', handleFileSelect)

        // 上传按钮
        uploadBtn.onclick = function () {
          addDebugInfo('上传按钮被点击')
          if (!selectedFile) {
            addDebugInfo('没有选择文件')
            return
          }
          uploadFile(selectedFile)
        }
      }

      // 处理文件选择
      function handleFileSelect(event) {
        const file = event.target.files[0]
        addDebugInfo(`文件选择事件: ${file ? file.name : '无文件'}`)

        if (file) {
          validateAndSetFile(file)
        }
      }

      // 验证并设置文件
      function validateAndSetFile(file) {
        addDebugInfo(`验证文件: ${file.name}, 大小: ${file.size}, 类型: ${file.type}`)

        if (file.type !== 'application/pdf') {
          addDebugInfo('文件类型不匹配')
          return
        }

        if (file.size > uploadConfig.maxFileSize) {
          addDebugInfo('文件大小超限')
          return
        }

        selectedFile = file
        uploadBtn.disabled = false
        uploadBtn.textContent = '上传文件'
        addDebugInfo('文件验证通过')

        // 自动触发上传（2秒后）
        setTimeout(() => {
          addDebugInfo('自动触发上传')
          uploadFile(selectedFile)
        }, 2000)
      }

      // 上传文件
      function uploadFile(file) {
        addDebugInfo(`开始上传文件: ${file.name}`)

        const formData = new FormData()
        formData.append('file', file)

        const xhr = new XMLHttpRequest()

        // 进度监听
        xhr.upload.addEventListener('progress', function (event) {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100
            addDebugInfo(`上传进度: ${progress.toFixed(1)}%`)

            sendMessageToUniApp({
              type: 'UPLOAD_PROGRESS',
              data: { progress },
            })
          }
        })

        // 完成监听
        xhr.addEventListener('load', function () {
          addDebugInfo(`上传响应状态: ${xhr.status}`)
          addDebugInfo(`上传响应文本: ${xhr.responseText}`)

          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              addDebugInfo(`解析的响应: ${JSON.stringify(response, null, 2)}`)
              handleUploadSuccess(response)
            } catch (error) {
              addDebugInfo(`响应解析失败: ${error.message}`)
              handleUploadError('响应解析失败')
            }
          } else {
            addDebugInfo(`上传失败，状态码: ${xhr.status}`)
            handleUploadError('上传失败: ' + xhr.status)
          }
        })

        // 错误监听
        xhr.addEventListener('error', function (error) {
          addDebugInfo(`上传网络错误: ${error}`)
          handleUploadError('网络错误')
        })

        // 超时监听
        xhr.addEventListener('timeout', function () {
          addDebugInfo('上传超时')
          handleUploadError('上传超时')
        })

        // 发送请求
        xhr.open('POST', uploadConfig.uploadUrl)

        // 设置请求头
        if (uploadConfig.headers) {
          Object.keys(uploadConfig.headers).forEach((key) => {
            xhr.setRequestHeader(key, uploadConfig.headers[key])
          })
        }

        xhr.timeout = uploadConfig.timeout || 30000
        xhr.send(formData)
        addDebugInfo('XHR请求已发送')
      }

      // 处理上传成功
      function handleUploadSuccess(response) {
        addDebugInfo('=== 处理上传成功 ===')

        uploadBtn.textContent = '上传成功'
        uploadBtn.style.backgroundColor = '#28a745'

        const message = {
          type: 'UPLOAD_SUCCESS',
          data: response,
        }

        addDebugInfo(`准备发送成功消息: ${JSON.stringify(message, null, 2)}`)
        sendMessageToUniApp(message)

        // 延迟重发确保消息被接收
        setTimeout(() => {
          addDebugInfo('延迟重发成功消息')
          sendMessageToUniApp(message)
        }, 1000)
      }

      // 处理上传错误
      function handleUploadError(error) {
        addDebugInfo(`处理上传错误: ${error}`)

        uploadBtn.disabled = false
        uploadBtn.textContent = '上传文件'
        uploadBtn.style.backgroundColor = '#007aff'

        sendMessageToUniApp({
          type: 'UPLOAD_ERROR',
          data: error,
        })
      }

      // 通知UniApp就绪
      function notifyUniAppReady() {
        addDebugInfo('通知UniApp就绪')
        sendMessageToUniApp({
          type: 'WEBVIEW_READY',
        })
      }

      // 接收UniApp消息
      window.addEventListener('message', function (event) {
        addDebugInfo(`收到window消息: ${JSON.stringify(event.data)}`)
        const message = event.data

        // 忽略自己发送的消息，避免循环
        if (message && message.type === 'INIT_CONFIG') {
          uploadConfig = { ...uploadConfig, ...message.data }
          addDebugInfo(`收到配置: ${JSON.stringify(uploadConfig, null, 2)}`)
        } else if (
          message &&
          (message.type === 'UPLOAD_SUCCESS' ||
            message.type === 'UPLOAD_PROGRESS' ||
            message.type === 'UPLOAD_ERROR')
        ) {
          addDebugInfo(`忽略自己发送的消息: ${message.type}`)
        }
      })

      // 兼容uni-app的消息接收方式
      if (window.uni && window.uni.onMessage) {
        window.uni.onMessage(function (event) {
          addDebugInfo(`收到uni消息: ${JSON.stringify(event.data)}`)
          const message = event.data

          if (message && message.type === 'INIT_CONFIG') {
            uploadConfig = message.data
            addDebugInfo(`收到配置: ${JSON.stringify(uploadConfig, null, 2)}`)
          }
        })
      }

      // 测试函数
      function testSuccessMessage() {
        addDebugInfo('测试成功消息')
        const testMessage = {
          type: 'UPLOAD_SUCCESS',
          data: {
            fileId: 'test-success-id',
            fileName: 'test-success.pdf',
            fileSize: 1024,
          },
        }
        sendMessageToUniApp(testMessage)
      }

      function testErrorMessage() {
        addDebugInfo('测试错误消息')
        const testMessage = {
          type: 'UPLOAD_ERROR',
          data: '测试错误信息',
        }
        sendMessageToUniApp(testMessage)
      }

      function testProgressMessage() {
        addDebugInfo('测试进度消息')
        const testMessage = {
          type: 'UPLOAD_PROGRESS',
          data: { progress: 50 },
        }
        sendMessageToUniApp(testMessage)
      }

      function testUniMessage() {
        addDebugInfo('测试uni消息发送')
        if (window.uni && window.uni.postMessage) {
          window.uni.postMessage({
            data: {
              type: 'TEST_UNI_MESSAGE',
              data: { test: 'uni message test' },
            },
          })
          addDebugInfo('uni消息已发送')
        } else {
          addDebugInfo('uni.postMessage不可用')
        }
      }

      function testDirectCall() {
        addDebugInfo('测试直接调用父页面方法')
        const testMessage = {
          type: 'UPLOAD_SUCCESS',
          data: {
            fileId: 'direct-call-test',
            fileName: 'direct-call.pdf',
            fileSize: 2048,
          },
        }

        try {
          if (window.parent && window.parent.handleWebViewMessage) {
            window.parent.handleWebViewMessage({ detail: testMessage })
            addDebugInfo('直接调用成功')
          } else {
            addDebugInfo('父页面方法不可用')
          }
        } catch (error) {
          addDebugInfo(`直接调用失败: ${error.message}`)
        }
      }

      // 暴露测试函数到全局
      window.testSuccessMessage = testSuccessMessage
      window.testErrorMessage = testErrorMessage
      window.testProgressMessage = testProgressMessage
      window.testUniMessage = testUniMessage
      window.testDirectCall = testDirectCall
    </script>
  </body>
</html>
