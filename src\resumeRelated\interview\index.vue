<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="待面试">
      <template #right>
        <view class="text-28rpx c-#000" @click="goHistory">历史面试</view>
      </template>
    </CustomNavBar>
    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 40rpx)` }"
    >
      <view class="pageList">
        <view class="c-#888 text-32rpx p-b-20rpx p-l-40rpx" v-if="pageData.length > 0">时间</view>
        <view
          class="pageList-item flex items-start"
          v-for="(item, index) in pageData"
          :key="index"
          @click="goDetail(item.id, item.companyId)"
        >
          <view class="pageList-item-left">
            <view class="c-#000 text-24rpx">
              {{ item.agreeTime.slice(5, 7) }}月{{ item.agreeTime.slice(8, 10) }}日
            </view>
            <view class="c-#000 text-34rpx text-right">{{ item.agreeTime.slice(11, 13) }}点</view>
          </view>
          <view class="pageList-item-right m-l-30rpx pageList-right p-l-40rpx p-b-40rpx">
            <view
              class="pageList-item-right-card relative"
              :class="item.status === 0 || item.status === 1 ? 'bg-#4399ff' : 'bg-#F2F2F2'"
            >
              <view class="flex items-center">
                <view class="w-90rpx">
                  <image
                    class="w-76rpx h-76rpx rounded-full"
                    :src="item.headImgUrl"
                    mode="aspectFill"
                  ></image>
                </view>
                <view class="flex-1">
                  <view class="flex items-center justify-between">
                    <view
                      class="text-28rpx p-b-5rpx u-line-1 w-340rpx"
                      :class="item.status === 0 || item.status === 1 ? 'c-#fff' : 'c-#000'"
                    >
                      {{ truncateText(item.name, 10) }}
                    </view>
                  </view>

                  <view class="flex justify-between">
                    <view
                      class="text-28rpx"
                      :class="item.status === 0 || item.status === 1 ? 'c-#fff' : 'c-#000'"
                    >
                      {{ item.positionName }}
                    </view>
                    <view
                      class="text-24rpx"
                      :class="item.status === 0 || item.status === 1 ? 'c-#fff' : 'c-#000'"
                    >
                      {{ item.workSalaryStart
                      }}{{ item.workSalaryEnd ? '-' + item.workSalaryEnd : '' }}
                    </view>
                  </view>
                </view>
              </view>
              <view
                class="absolute top-[0rpx] right-[-10rpx] z-100"
                v-if="item.status === 0 || item.status === 1"
              >
                <wd-img :width="35" :height="35" :src="interview" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryWaitMeetingList } from '@/interPost/resume'
import { numberTokw } from '@/utils/common'
import { getCustomBar } from '@/utils/storage'
import interview from '@/sub_business/static/interview/interview.png'
import { truncateText } from '@/utils/util'
const { pagingRef, pageData, pageStyle } = usePaging({
  style: {
    padding: '80rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '40rpx',
  },
})
const customBar = ref(null)
// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}
const goDetail = (id: any, companyId: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId}`,
  })
}
const queryList = async () => {
  const res: any = await queryWaitMeetingList()
  if (res.code === 0) {
    res.data &&
      res.data.forEach((ele: any) => {
        ele.positionKey = ele.positionKey && ele.positionKey.split(',')
        ele.workSalaryStart =
          ele.workSalaryStart === 0 ? '面议' : numberTokw(ele.workSalaryStart + '')
        ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
        if (!ele.headImgUrl) {
          ele.headImgUrl =
            ele.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
        }
      })

    pagingRef.value.complete(res.data)
  }
}
onLoad(async (options) => {
  await nextTick()
  customBar.value = getCustomBar()
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
.pageList-right {
  border-left: 1rpx solid #cccaca;
}
.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  background: #4399ff;
  border-radius: 20rpx;
}
</style>
