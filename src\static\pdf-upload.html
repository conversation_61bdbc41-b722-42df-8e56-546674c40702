<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
    />
    <title>PDF上传</title>
    <style>
      * {
        box-sizing: border-box;
        padding: 0;
        margin: 0;
      }

      body {
        display: flex;
        flex-direction: column;
        height: 100vh;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: #333;
        background-color: #f5f5f5;
      }

      .container {
        display: flex;
        flex: 1;
        flex-direction: column;
        max-width: 600px;
        padding: 20px;
        margin: 0 auto;
      }

      .header {
        padding: 20px 0;
        text-align: center;
      }

      .header h1 {
        margin-bottom: 10px;
        font-size: 24px;
        color: #007aff;
      }

      .header p {
        font-size: 14px;
        color: #666;
      }

      .upload-area {
        display: flex;
        flex: 1;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        margin: 20px 0;
        text-align: center;
        cursor: pointer;
        background: white;
        border: 2px dashed #007aff;
        border-radius: 12px;
        transition: all 0.3s ease;
      }

      .upload-area:hover {
        background-color: #f8f9fa;
        border-color: #0056b3;
      }

      .upload-icon {
        margin-bottom: 20px;
        font-size: 64px;
        color: #007aff;
      }

      .upload-text {
        margin-bottom: 8px;
        font-size: 18px;
        font-weight: 500;
        color: #333;
      }

      .upload-hint {
        margin-top: 10px;
        font-size: 14px;
        color: #999;
      }

      .file-input {
        position: fixed;
        bottom: 20rpx;
        display: none;
      }

      .file-info {
        display: none;
        padding: 15px;
        margin-top: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }

      .file-info.show {
        display: block;
      }

      .file-name {
        margin-bottom: 8px;
        overflow: hidden;
        font-size: 16px;
        font-weight: 500;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .file-size {
        font-size: 14px;
        color: #666;
      }

      .progress-container {
        margin-top: 15px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        overflow: hidden;
        background-color: #e9ecef;
        border-radius: 4px;
      }

      .progress-fill {
        width: 0%;
        height: 100%;
        background: linear-gradient(90deg, #007aff, #00aaff);
        border-radius: 4px;
        transition: width 0.3s ease;
      }

      .progress-text {
        margin-top: 5px;
        font-size: 12px;
        color: #666;
        text-align: right;
      }

      .upload-btn {
        display: block;
        width: 100%;
        padding: 16px 20px;
        margin-top: auto;
        margin-bottom: 20px;
        font-size: 16px;
        font-weight: 500;
        color: white;
        cursor: pointer;
        background: linear-gradient(90deg, #007aff, #00aaff);
        border: none;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 122, 255, 0.3);
        transition: all 0.3s ease;
      }

      .upload-btn:hover {
        opacity: 0.9;
      }

      .upload-btn:disabled {
        cursor: not-allowed;
        background: #6c757d;
      }

      .error-message {
        display: none;
        padding: 12px;
        margin: 15px 0;
        color: #721c24;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
      }

      .error-message.show {
        display: block;
      }

      .status-panel {
        padding: 15px;
        margin-top: 20px;
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }

      .panel-title {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: 600;
        color: #007aff;
      }

      .panel-title i {
        margin-right: 8px;
      }

      .status-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
      }

      .status-item {
        padding: 10px;
        font-size: 13px;
        background: #f8f9fa;
        border-radius: 6px;
      }

      .status-label {
        margin-bottom: 4px;
        color: #666;
      }

      .status-value {
        font-weight: 500;
        color: #333;
        word-break: break-all;
      }

      .status-value.ready {
        color: #28a745;
      }

      .status-value.error {
        color: #dc3545;
      }

      .loading-overlay {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 1000;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.9);
      }

      .spinner {
        width: 40px;
        height: 40px;
        margin-bottom: 15px;
        border: 4px solid rgba(0, 122, 255, 0.2);
        border-top: 4px solid #007aff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>PDF文件上传</h1>
        <p>上传您的PDF文件，最大支持20MB</p>
      </div>

      <div class="upload-area" id="uploadArea">
        <div class="upload-icon">📄</div>
        <div class="upload-text">点击上传PDF文件</div>
        <div class="upload-hint">最大20MB，仅支持PDF格式</div>
        <input
          type="file"
          id="fileInput"
          class="file-input"
          accept=".pdf,application/pdf"
          capture="false"
        />
      </div>

      <div class="error-message" id="errorMessage"></div>

      <div class="file-info" id="fileInfo">
        <div class="file-name" id="fileName"></div>
        <div class="file-size" id="fileSize"></div>
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
          </div>
          <div class="progress-text" id="progressText">0%</div>
        </div>
      </div>

      <button class="upload-btn" id="uploadBtn">等待配置...</button>

      <div class="status-panel">
        <div class="panel-title">
          <i>📊</i>
          当前状态
        </div>
        <div class="status-grid">
          <div class="status-item">
            <div class="status-label">UniApp状态</div>
            <div class="status-value" id="uniStatus">检测中...</div>
          </div>
          <div class="status-item">
            <div class="status-label">配置状态</div>
            <div class="status-value" id="configStatus">未配置</div>
          </div>
          <div class="status-item">
            <div class="status-label">文件状态</div>
            <div class="status-value" id="fileStatus">未选择</div>
          </div>
          <div class="status-item">
            <div class="status-label">上传地址</div>
            <div class="status-value" id="uploadUrl">-</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载uni-app SDK -->
    <script
      type="text/javascript"
      src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.2.js"
    ></script>

    <script>
      // 全局变量
      let selectedFile = null
      let uploadConfig = {
        maxFileSize: 20 * 1024 * 1024, // 20MB
        allowedTypes: ['application/pdf'],
        uploadUrl: '',
        headers: {},
        timeout: 300000,
        additionalData: { type: 'resume' },
      }
      let isUniAppReady = false

      // DOM元素
      const uploadArea = document.getElementById('uploadArea')
      const fileInput = document.getElementById('fileInput')
      const fileInfo = document.getElementById('fileInfo')
      const fileName = document.getElementById('fileName')
      const fileSize = document.getElementById('fileSize')
      const progressFill = document.getElementById('progressFill')
      const progressText = document.getElementById('progressText')
      const uploadBtn = document.getElementById('uploadBtn')
      const errorMessage = document.getElementById('errorMessage')
      const uniStatus = document.getElementById('uniStatus')
      const configStatus = document.getElementById('configStatus')
      const fileStatus = document.getElementById('fileStatus')
      const uploadUrl = document.getElementById('uploadUrl')

      // 更新状态面板
      function updateStatusPanel() {
        uniStatus.textContent = isUniAppReady ? '已就绪' : '未就绪'
        uniStatus.className = isUniAppReady ? 'status-value ready' : 'status-value'

        configStatus.textContent = uploadConfig.uploadUrl ? '已配置' : '未配置'
        configStatus.className = uploadConfig.uploadUrl ? 'status-value ready' : 'status-value'

        fileStatus.textContent = selectedFile ? '已选择' : '未选择'
        fileStatus.className = selectedFile ? 'status-value ready' : 'status-value'

        uploadUrl.textContent = uploadConfig.uploadUrl || '-'
        uploadUrl.className = uploadConfig.uploadUrl ? 'status-value ready' : 'status-value'
      }

      // 统一更新上传按钮状态
      function updateUploadBtnStatus() {
        const hasFile = !!selectedFile
        const hasConfig = !!(uploadConfig && uploadConfig.uploadUrl)
        const shouldEnable = hasFile && hasConfig && isUniAppReady

        uploadBtn.disabled = !shouldEnable

        if (!shouldEnable) {
          uploadBtn.textContent = '等待配置...'
        } else {
          uploadBtn.textContent = '上传文件'
        }

        updateStatusPanel()
      }

      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        console.log('DOMContentLoaded事件触发')

        // 尝试从URL参数获取token
        const urlParams = new URLSearchParams(window.location.search)
        const token = urlParams.get('token') || localStorage.getItem('token') || ''

        if (token) {
          uploadConfig.headers.token = token
          console.log('从URL参数或localStorage获取到token')
        }

        initEventListeners()
        updateUploadBtnStatus()
        updateStatusPanel()
      })

      // 等待UniApp就绪
      document.addEventListener('UniAppJSBridgeReady', function () {
        console.log('UniAppJSBridgeReady事件触发')
        isUniAppReady = true

        // 通知Vue页面WebView已就绪
        notifyUniAppReady()
        updateUploadBtnStatus()
      })

      // 初始化事件监听器
      function initEventListeners() {
        // 点击上传区域
        uploadArea.addEventListener('click', () => {
          fileInput.setAttribute('capture', 'false')
          fileInput.click()
        })

        // 文件选择
        fileInput.addEventListener('change', handleFileSelect)

        // 上传按钮点击
        uploadBtn.onclick = function (event) {
          if (!selectedFile) {
            showError('请先选择文件')
            return
          }
          if (!uploadConfig || !uploadConfig.uploadUrl) {
            showError('上传配置错误: 缺少上传URL')
            return
          }
          if (!isUniAppReady) {
            showError('UniApp未就绪')
            return
          }

          // 重置进度条
          updateProgress(0)
          uploadBtn.disabled = true
          uploadBtn.textContent = '上传中...'
          uploadFile(selectedFile)
        }
      }

      // 处理文件选择
      function handleFileSelect(event) {
        const file = event.target.files[0]
        console.log('选择的文件:', file)

        if (file) {
          validateAndSetFile(file)
        }
      }

      // 验证并设置文件
      function validateAndSetFile(file) {
        // 验证文件类型
        if (file.type !== 'application/pdf') {
          showError('请选择PDF格式的文件')
          return
        }

        // 验证文件大小
        const maxSize = uploadConfig?.maxFileSize || 10 * 1024 * 1024
        if (file.size > maxSize) {
          showError('文件大小超过限制')
          return
        }

        selectedFile = file
        displayFileInfo(file)
        hideError()
        updateUploadBtnStatus()

        // 通知UniApp文件已选择
        sendMessageToUniApp({
          type: 'FILE_SELECTED',
          data: {
            name: file.name,
            size: file.size,
            type: file.type,
          },
        })
      }

      // 显示文件信息
      function displayFileInfo(file) {
        fileName.textContent = file.name
        fileSize.textContent = formatFileSize(file.size)
        fileInfo.classList.add('show')
      }

      // 格式化文件大小
      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }

      // 上传文件
      function uploadFile(file) {
        if (!file || !uploadConfig || !uploadConfig.uploadUrl) {
          console.error('上传参数错误')
          uploadBtn.disabled = false
          return
        }

        const formData = new FormData()
        formData.append('file', file)

        const xhr = new XMLHttpRequest()

        // 进度监听
        xhr.upload.addEventListener('progress', function (event) {
          if (event.lengthComputable) {
            const progress = (event.loaded / event.total) * 100
            updateProgress(progress)

            sendMessageToUniApp({
              type: 'UPLOAD_PROGRESS',
              data: { progress },
            })
          }
        })

        // 完成监听
        xhr.addEventListener('load', function () {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText)
              handleUploadSuccess(response)
            } catch (error) {
              handleUploadError('响应解析失败')
            }
          } else {
            handleUploadError('上传失败: ' + xhr.status)
          }
        })

        // 错误监听
        xhr.addEventListener('error', function (error) {
          handleUploadError('网络错误')
        })

        // 超时监听
        xhr.addEventListener('timeout', function () {
          handleUploadError('上传超时')
        })

        // 发送请求
        xhr.open('POST', uploadConfig.uploadUrl)

        // 设置请求头
        if (uploadConfig.headers) {
          Object.keys(uploadConfig.headers).forEach((key) => {
            xhr.setRequestHeader(key, uploadConfig.headers[key])
          })
        }

        // 设置超时
        xhr.timeout = uploadConfig.timeout || 30000

        try {
          xhr.send(formData)
        } catch (error) {
          handleUploadError('发送请求失败: ' + error.message)
        }
      }

      // 更新进度
      function updateProgress(progress) {
        progress = Math.min(100, Math.max(0, progress)) // 确保在0-100之间
        progressFill.style.width = progress + '%'
        progressText.textContent = Math.round(progress) + '%'
      }

      // 处理上传成功
      function handleUploadSuccess(response) {
        console.log('上传成功:', response)

        // 更新按钮状态
        uploadBtn.textContent = '上传成功'
        uploadBtn.style.background = '#28a745'

        // 发送成功消息
        sendMessageToUniApp({
          type: 'UPLOAD_SUCCESS',
          data: response,
        })

        // 3秒后重置
        setTimeout(() => {
          resetUploader()
        }, 3000)
      }

      // 处理上传错误
      function handleUploadError(error) {
        uploadBtn.disabled = false
        uploadBtn.textContent = '上传文件'
        uploadBtn.style.background = 'linear-gradient(90deg, #007aff, #00aaff)'

        showError(error)

        sendMessageToUniApp({
          type: 'UPLOAD_ERROR',
          data: error,
        })
      }

      // 重置上传器
      function resetUploader() {
        selectedFile = null
        fileInput.value = ''
        fileInfo.classList.remove('show')
        updateProgress(0)
        updateUploadBtnStatus()
        uploadBtn.style.background = 'linear-gradient(90deg, #007aff, #00aaff)'
      }

      // 显示错误
      function showError(message) {
        errorMessage.textContent = message
        errorMessage.classList.add('show')
      }

      // 隐藏错误
      function hideError() {
        errorMessage.classList.remove('show')
      }

      // 通知UniApp就绪
      function notifyUniAppReady() {
        sendMessageToUniApp({
          type: 'WEBVIEW_READY',
        })
      }

      // 发送消息到UniApp
      function sendMessageToUniApp(message) {
        console.log('发送消息到UniApp:', message)

        // 使用uni.postMessage发送消息（官方方式）
        if (typeof uni !== 'undefined' && uni.postMessage) {
          try {
            uni.postMessage({
              data: [message],
            })
            console.log('通过uni.postMessage发送消息成功')
          } catch (error) {
            console.error('uni.postMessage发送失败:', error)
          }
        }
      }

      // 接收UniApp消息
      window.addEventListener('message', function (event) {
        console.log('收到window消息:', event.data)
        const message = event.data

        if (message && message.type === 'INIT_CONFIG') {
          // 合并外部配置和默认配置
          uploadConfig = { ...uploadConfig, ...message.data }
          console.log('收到配置:', uploadConfig)
          updateUploadBtnStatus()

          // 发送确认消息
          sendMessageToUniApp({
            type: 'INIT_CONFIG',
            data: { received: true },
          })
        }
      })
      // 添加全局消息处理函数
      window.handleMessageFromVue = function (message) {
        console.log('通过handleMessageFromVue收到消息:', message)
        handleMessage(message)
      }

      // 统一消息处理
      function handleMessage(message) {
        console.log('处理收到的消息:', message)

        if (message && message.type === 'INIT_CONFIG') {
          console.log('收到配置消息:', message.data)

          // 合并外部配置和默认配置
          uploadConfig = { ...uploadConfig, ...message.data }
          console.log('合并后的配置:', JSON.stringify(uploadConfig, null, 2))

          // 更新UI状态
          updateUploadBtnStatus()
          updateStatusPanel()

          // 发送确认消息
          sendMessageToUniApp({
            type: 'INIT_CONFIG',
            data: { received: true },
          })
        }
      }

      // 监听消息 - 统一处理入口
      window.addEventListener('message', function (event) {
        console.log('收到window消息:', event.data)
        handleMessage(event.data)
      })

      // 兼容uni-app的消息接收方式
      if (window.uni && window.uni.onMessage) {
        window.uni.onMessage(function (event) {
          console.log('收到uni消息:', event.data)
          handleMessage(event.data)
        })
      }

      // 初始化
      document.addEventListener('DOMContentLoaded', function () {
        // ... 其他初始化代码 ...

        // 添加额外的事件监听
        window.addEventListener('load', function () {
          console.log('窗口完全加载完成')
          // 如果还没有收到配置，尝试请求
          if (!uploadConfig.uploadUrl) {
            console.log('未收到配置，发送就绪通知')
            notifyUniAppReady()
          }
        })
      })

      // 通知UniApp就绪 - 增加重试机制
      function notifyUniAppReady() {
        console.log('发送WEBVIEW_READY通知')
        sendMessageToUniApp({
          type: 'WEBVIEW_READY',
        })

        // 如果没有收到响应，5秒后重试
        setTimeout(() => {
          if (!uploadConfig.uploadUrl) {
            console.log('未收到配置响应，重试发送WEBVIEW_READY')
            notifyUniAppReady()
          }
        }, 5000)
      }
      // 兼容uni-app的消息接收方式
      if (window.uni && window.uni.onMessage) {
        window.uni.onMessage(function (event) {
          console.log('收到uni消息:', event.data)
          const message = event.data

          if (message && message.type === 'INIT_CONFIG') {
            uploadConfig = { ...uploadConfig, ...message.data }
            console.log('收到配置:', uploadConfig)
            updateUploadBtnStatus()

            // 发送确认消息
            sendMessageToUniApp({
              type: 'INIT_CONFIG',
              data: { received: true },
            })
          }
        })
      }

      // 添加调试信息
      console.log('PDF上传页面已加载')
      console.log('当前环境:', navigator.userAgent)
      console.log('uploadConfig初始值:', uploadConfig)
    </script>
  </body>
</html>
