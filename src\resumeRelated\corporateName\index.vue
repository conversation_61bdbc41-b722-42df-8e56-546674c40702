<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="公司名称">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
        <template #right>
          <view class="submit" @click="submit">确认</view>
        </template>
      </CustomNavBar>
      <view class="corporateName">
        <wd-input
          no-border
          focus
          v-model="company"
          placeholder="请输入公司名称"
          @input="handleChange"
        />
      </view>
    </template>
    <view class="content">
      <!-- 搜索结果列表 -->
      <view class="search-result" v-if="searchResults.length > 0">
        <view
          class="result-item"
          v-for="(item, index) in searchResults"
          :key="index"
          @click="selectSchool(item)"
        >
          {{ item.companyName }}
        </view>
      </view>
    </view>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { useResumeStore } from '@/store'
import { companyOptionList } from '@/interPost/common'
import { debounce } from 'lodash-es'
import { useMessage } from 'wot-design-uni'

// z-paging配置
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const resumeStore = useResumeStore()
const company = ref('') // 当前输入的学校名称
const initNane = ref('') // 初始学校名称
const searchResults = ref<string[]>([]) // 搜索结果列表
const loading = ref(false) // 加载状态
const companyId = ref(null)
const message = useMessage()

// 防抖处理后的搜索函数
const debouncedSearch = debounce(async (value: string) => {
  if (!value.trim()) {
    searchResults.value = []
    return
  }

  try {
    loading.value = true
    const res = await companyOptionList({
      entity: {
        companyName: value,
      },
      page: 1,
      size: 50,
    })
    searchResults.value = res.data || []
  } catch (error) {
    console.error('搜索公司失败:', error)
    searchResults.value = []
  } finally {
    loading.value = false
  }
}, 500) // 500ms防抖

// input输入处理
const handleChange = ({ value }: { value: string }) => {
  company.value = value
  debouncedSearch(value)
}

// 选择学校
const selectSchool = (item: any) => {
  company.value = item.companyName
  companyId.value = item.id
  searchResults.value = [] // 选择后清空搜索结果
}

// 提交
const submit = () => {
  resumeStore.setCompany(company.value)
  resumeStore.setCompanyId(companyId.value)
  uni.navigateBack()
}

// 返回处理
const back = () => {
  if (company.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}

// 页面加载时处理路由参数
onLoad(async (options) => {
  if (options?.company) {
    company.value = options.company
    initNane.value = options.company
  }
})
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx !important;
}
::v-deep .wd-input__inner {
  font-size: 32rpx !important;
  font-weight: 500;
}

.content {
  padding: 0 40rpx;
}

.corporateName {
  padding: 40rpx;
  margin: 0rpx 40rpx 40rpx;
  border-bottom: 1rpx solid #c0bfbf;
}

.search-result {
  // max-height: 500rpx;
  // overflow-y: auto;
  // background-color: #fff;
  // border-radius: 12rpx;
  // box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

  .result-item {
    padding: 24rpx 32rpx;
    font-size: 32rpx;
    border-bottom: 1rpx solid #e1dfdf;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      // background-color: #f9f9f9;
    }
  }
}

.submit {
  padding-right: 20rpx;
  font-size: 28rpx;
  color: #333;
}
</style>
