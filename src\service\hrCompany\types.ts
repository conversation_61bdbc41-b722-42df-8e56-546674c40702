/** 人员规模 */
export interface hrCompanySizeInt {
  sizeName: string
}
// 公司简称
export interface hrCompanyShortName {
  shortName: string
}
// 行业
export interface hrCompanyIndustry {
  industryCode: string
  industryName: string
}
// 时间
export interface hrCompanyTime {
  workEndTime: string
  workStartTime: string
}
// 公司介绍
export interface hrCompanyProfile {
  profile: string
}
// 福利待遇
export interface hrCompanywelfare {
  welfareList: string[]
}
// 上传图像

export interface hrCompanyLogo {
  companyLogoId: string
}

// 公司风采新增
export interface hrStyleAddStyle {
  attachId: number
  attachType: number
}

// 公司风采删除
export interface hrStyleDel {
  id: number
}

// 公司风采列表
export interface hrStyleList {
  id: number
}

// hr职位
export interface hrStylePosition {
  hrPosition: string
}
