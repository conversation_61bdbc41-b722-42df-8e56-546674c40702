<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    :paging-style="pageStyle"
    @query="queryList"
    :fixed="false"
    auto-clean-list-when-reload
    show-refresher-when-reload
    :empty-view-super-style="{ justifyContent: 'flex-start' }"
  >
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <view class="pr-52rpx pl-26rpx py-32rpx flex items-center justify-between">
          <view class="w-200rpx">
            <wd-tabs v-model="jobTabsStatus" @change="handleChangeTab">
              <wd-tab
                v-for="item in jobStatusTabsList"
                :key="item.name"
                :title="`${item.label}`"
                :name="item.name"
              />
            </wd-tabs>
          </view>
          <view class="flex items-center gap-4rpx">
            <text class="c-#555555 text-24rpx">筛选</text>
            <text class="i-carbon-triangle-down-solid text-12rpx c-#333333" />
          </view>
        </view>
      </wd-config-provider>
    </template>
    <template #empty>
      <view class="flex flex-col items-center justify-center">
        <wd-img :src="releasePostEmpty" width="412rpx" height="412rpx" />
        <text class="c-#000000 text-28rpx">
          {{ useReleasePost ? '' : '未发布职位，无法查看简历' }}
        </text>
      </view>
    </template>
    <view class="p-[0_30rpx_44rpx] flex flex-col gap-40rpx">
      <personal-list
        v-for="(item, key) in pageData"
        :key="key"
        :list="item"
        :position="releaseActivePost"
      />
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import { hrIndexResumeUserList } from '@/service/hrIndex'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import personalList from '@/components/common/personal-list.vue'
import releasePostEmpty from '@/static/home/<USER>/release-post-empty.png'

defineOptions({
  name: 'HomeBusiness',
})

const { releaseActivePost, releaseIsHavePost } = useReleasePost()
const { pagingRef, pageInfo, pageSetInfo, pageData, pageStyle } =
  usePaging<hrIndexResumeUserListInt>({
    style: {},
  })
const jobTabsStatus = ref<Api.Common.EnableStatus>(0)
const jobStatusTabsList = [
  {
    label: '推荐',
    name: 0,
  },
  {
    label: '最新',
    name: 1,
  },
]
const themeVars: ConfigProviderThemeVars = {
  navbarHeight: '120rpx',
  tabsNavLineBgColor: '#FF9191',
  tabsNavHeight: '48rpx',
  tabsNavLineHeight: '4rpx',
  tabsNavLineWidth: '58rpx',
}

async function fetchHrIndexResumeUserList() {
  if (!releaseIsHavePost.value) {
    pagingRef.value.completeByTotal([], 0)
    return
  }
  const { data } = await hrIndexResumeUserList({
    entity: {
      positionInfoId: releaseActivePost.value.id,
      searchType: jobTabsStatus.value,
    },
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}
function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  fetchHrIndexResumeUserList()
}
async function reload() {
  pagingRef.value.reload()
}
function handleChangeTab({ name }) {
  jobTabsStatus.value = name
  reload()
}

defineExpose({
  reload,
})
</script>

<style lang="scss" scoped>
:deep(.zp-view-super) {
  margin: 0 !important;
}
:deep(.zp-paging-container-content) {
  height: auto !important;
}
:deep(.wd-navbar__left) {
  align-items: end;
}
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__line {
    bottom: 2px;
  }
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    &.is-active {
      font-size: 32rpx;
    }
  }
}
</style>
