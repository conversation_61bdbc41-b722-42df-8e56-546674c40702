<template>
  <view class="page_list">
    <view class="page_flex" v-for="(listItem, listIndex) in jobList" :key="`${index}-${listIndex}`">
      <view class="page_flex_colom" @click="goDetail(listItem.id, listItem.companyId)">
        <view class="page_flex_list">
          <view class="flex-c">
            <wd-img :width="33" :height="16" :src="jz" v-if="listItem.isRecruit === 1" />
            <view class="page_left">
              {{ truncateText(listItem.positionName, 8) }}
            </view>
            <view class="stateType" v-if="listItem.jobType === 2 || listItem.jobType === 3">
              {{ listItem.jobType === 2 ? '兼职' : listItem.jobType === 3 ? '实习' : '' }}
            </view>
          </view>

          <view class="page_right salary">
            <text>{{ listItem.workSalaryBegin }}</text>
            <text v-if="listItem.workSalaryEnd">-</text>
            <text v-if="listItem.workSalaryEnd">
              {{ listItem.workSalaryEnd
              }}{{ listItem?.salaryMonths === 12 ? '/月' : '·' + listItem.salaryMonths + '薪' }}
            </text>
          </view>
        </view>
        <view class="page_flex_list">
          <view class="flex items-center">
            <view class="page_left_1">{{ truncateText(listItem.name, 14) }}</view>
            <view v-if="listItem.sizeName" class="page_left_1">·</view>
            <view v-if="listItem.sizeName" class="page_left_1">
              {{ listItem.sizeName }}
            </view>
          </view>
          <view class="page_right_flex">
            <!-- <wd-icon name="location" size="14px" color="#999"></wd-icon> -->
            <!-- <wd-img :width="10" :height="10" :src="location" /> -->
            <view class="page_right_distance">
              {{ listItem.distanceMeters ? listItem.distanceMeters : listItem.districtName }}
            </view>
          </view>
        </view>
        <view class="bg_flex" v-if="listItem.positionKey && listItem.positionKey.length > 0">
          <view
            class="bg_box"
            v-for="(subName, subIndex) in listItem.positionKey.slice(0, 4)"
            :key="subIndex"
          >
            {{ subName }}
          </view>
        </view>
        <view class="bg_end m-b-10rpx">
          <view class="bg_left relative">
            <!--  -->
            <view :class="listItem.hxUserInfoVO?.isOnline ? 'border-twinkle bg_left_icon_box' : ''">
              <image class="bg_left_icon" :src="listItem.hrPositionUrl" mode="aspectFill"></image>
            </view>

            <view class="bg_left_flex">
              <view class="bg_left_name flex items-center">
                <view class="flex items-center">
                  <view class="c-#333 text-24rpx">{{ listItem.hrPositionName }}</view>
                </view>
                <view class="m-l-5rpx m-r-5rpx" v-if="listItem.hrPosition">·</view>
                <view class="c-#333 text-24rpx">
                  {{ truncateText(listItem.hrPosition, 6) }}
                </view>
              </view>
              <view class="bg_left_date">{{ listItem.activityStatus }}</view>
            </view>
          </view>
          <view class="flex-c">
            <view class="bg_right m-r-20rpx" @click.stop="goJob(listItem)">
              <image
                class="bg_right_icon-1"
                src="/static/img/td-job-card.png"
                mode="aspectFill"
              ></image>
            </view>
            <view class="bg_right" @click.stop="goChat(listItem)">
              <image
                class="bg_right_icon"
                src="/static/images/home/<USER>"
                mode="aspectFill"
              ></image>
            </view>
          </view>
        </view>
        <view class="p-t-20rpx relative flex items-center justify-between w-100 border-top">
          <view class="flex items-center justify-left w-45">
            <view
              class="flex items-center justify-center min-w-150rpx h-32rpx relative p-1px"
              style="
                clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%);
                background: linear-gradient(270deg, rgba(81, 150, 253, 1), rgba(255, 86, 86, 1));
              "
            >
              <view
                class="size-full bg-#ffffff flex items-center px-8rpx gap-4rpx"
                style="clip-path: polygon(8rpx 0, 100% 0, calc(100% - 8rpx) 100%, 0 100%)"
              >
                <wd-img :src="resumeMatching" width="70rpx" height="20rpx" />
                <text class="text-24rpx c-#DF6176 font-500 font-italic">
                  {{ listItem.competitiveness }}%
                </text>
              </view>
            </view>
            <view class="flex-1 flex items-center">
              <view
                class="w-182rpx h-28rpx shadow-[0rpx_0rpx_2rpx_0rpx_#FF8A8A] border-rd-full p-1px"
                style="
                  background: linear-gradient(
                    139deg,
                    rgba(217, 182, 253, 1),
                    rgba(162, 199, 253, 1),
                    rgba(153, 193, 253, 1),
                    rgba(250, 112, 145, 1)
                  );
                "
              >
                <view class="w-full h-full bg-white border-rd-full px-6rpx flex items-center">
                  <view
                    class="h-16rpx border-rd-full transition-all duration-1000 ease-in-out"
                    :style="{
                      width: `${listItem.competitiveness}%`,
                      background:
                        'linear-gradient(315deg, #FF6C8C 0%, #9AC2FD 47%, rgba(90,155,252,0.55) 85%, rgba(141,34,251,0.33) 98%)',
                    }"
                  />
                </view>
              </view>
            </view>
          </view>
          <view class="flex items-center justify-right w-45">
            <wd-img :width="27" :height="15" :src="pk" class="m-r-15rpx" />
            <view class="flex items-center">
              <wd-img
                v-for="(sub, subIndex) in Array.isArray(listItem.count) ? listItem.count : []"
                :key="subIndex"
                :width="20"
                :height="20"
                :src="`/static/img/header/${sub}.png`"
                round
                class="m-l-[-15rpx]"
              />
              <view
                v-if="listItem.matchingDegree > 2"
                class="w-40rpx h-40rpx rounded-full bg-#A1A0A0 c-#fff z-10 text-c text-18rpx leading-[40rpx] m-l-[-15rpx]"
              >
                {{ listItem.matchingDegree < 100 ? listItem.matchingDegree : '99' }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { truncateText } from '@/utils/util'
import jz from '@/static/img/home/<USER>'
import pk from '@/static/img/home/<USER>'
import resumeMatching from '@/static/common/resume-matching.png'
import location from '@/static/img/location.png'

interface JobItem {
  id: string | number
  companyId: string | number
  positionName: string
  isRecruit: number
  jobType: number
  workSalaryBegin: string
  workSalaryEnd: string
  name: string
  sizeName: string
  distanceMeters: string
  districtName: string
  positionKey: string[]
  hrPositionUrl: string
  hrPositionName: string
  hrPosition: string
  activityStatus: string
  competitiveness: number
  count: string[]
  matchingDegree: number
  hxUserInfoVO?: {
    isOnline: boolean
  }
}

interface Props {
  jobList: JobItem[]
  index?: number
  isShow?: boolean
}

interface Emits {
  (e: 'goDetail', id: string | number, companyId: string | number): void
  (e: 'goJob', item: JobItem): void
  (e: 'goChat', item: JobItem): void
}

const props = withDefaults(defineProps<Props>(), {
  index: 0,
  isShow: false,
})

const emit = defineEmits<Emits>()

const goDetail = (id: string | number, companyId: string | number) => {
  emit('goDetail', id, companyId)
}

const goJob = (item: JobItem) => {
  emit('goJob', item)
}

const goChat = (item: JobItem) => {
  emit('goChat', item)
}
</script>

<style lang="scss" scoped>
.page_left_1 {
  font-size: 24rpx !important;
  line-height: 44rpx;
  color: #666;
}

.bg_end {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding-top: 6rpx;

  .bg_right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 140rpx;
    height: 64rpx;
    text-align: center;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 4px 4px 16.5px 0px rgba(0, 0, 0, 0.1);

    &_icon {
      width: 50rpx;
      height: 50rpx;
    }

    &_icon-1 {
      width: 50rpx;
      height: 50rpx;
    }
  }

  .bg_left {
    display: flex;
    flex-direction: row;
    align-items: center;

    .bg_left_icon {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50rpx;
    }

    .bg_left_flex {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      padding-left: 15rpx;

      .bg_left_name {
        font-size: 24rpx;
        line-height: 44rpx;
        color: #555555;
      }

      .bg_left_date {
        font-size: 22rpx;
        color: #666;
      }
    }
  }
}

.bg_flex {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  width: 100%;
  padding-top: 5rpx !important;
  padding-bottom: 14rpx;

  .bg_box {
    padding: 5rpx 20rpx;
    margin-right: 15rpx;
    font-size: 22rpx;
    font-weight: 400;
    color: #888888;
    background: #f3f3f3;
    border-radius: 6rpx 6rpx 6rpx 6rpx;
  }
}

.page_list {
  box-sizing: border-box;
  width: 100%;
  padding: 0 40rpx;
  margin-bottom: 60rpx;

  .page_flex {
    width: 100%;
    padding: 20rpx 30rpx;
    margin-bottom: 20rpx;
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .page_flex_colom {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      width: 100%;

      .page_flex_list {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        width: 100%;

        .page_right_flex {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding: 5rpx 0;

          .page_left_1 {
            font-size: 24rpx !important;
            font-weight: 400;
            line-height: 44rpx;
            color: #666;
          }

          .page_right_distance {
            font-size: 22rpx;
            font-weight: 400;
            line-height: 44rpx;
            color: #888888;
          }
        }

        .page_left {
          font-size: 38rpx;
          font-weight: 500;
          line-height: 44rpx;
          color: #333333;
        }

        .page_right {
          font-size: 28rpx;
          font-weight: 500;
          line-height: 44rpx;
          color: #888888;
        }
      }
    }
  }
}

.border-twinkle {
  position: relative;

  &::before {
    position: absolute;
    top: -2rpx;
    right: -2rpx;
    bottom: -2rpx;
    left: -2rpx;
    z-index: -1;
    content: '';
    background: linear-gradient(45deg, #0ea500, #00ff00, #0ea500);
    border-radius: 50rpx;
    animation: twinkle 2s infinite;
  }
}

@keyframes twinkle {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.stateType {
  padding: 2rpx 8rpx;
  margin-left: 8rpx;
  font-size: 20rpx;
  color: #ff5151;
  background: rgba(255, 81, 81, 0.1);
  border-radius: 4rpx;
}

.border-top {
  border-top: 2rpx dashed #d8d8d8;
}

.salary {
  color: #ff8080 !important;
}
</style>
