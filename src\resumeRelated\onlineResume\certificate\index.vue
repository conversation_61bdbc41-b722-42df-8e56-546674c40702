<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="资格证书">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="setting">
      <view class="setting-card">
        <view class="flex-c border-b p-b-40rpx p-t-20rpx">
          <view class="mainText m-r-10rpx">证书名称</view>
          <wd-input
            class="flex-1"
            no-border
            v-model="certificate"
            border="none"
            custom-input-class="custom-class"
            :maxlength="20"
            show-word-limit
            placeholder="请输入证书名称"
          ></wd-input>
        </view>
        <wd-upload
          reupload
          :before-upload="beforeUpload"
          v-model:file-list="fileList"
          :limit="1"
          :header="header"
          :action="baseUrl"
          @success="successFun"
          style="margin: auto"
          custom-class="custom-class-1"
          accept="image"
          :max-size="20 * 1024 * 1024"
        >
          <view class="m-t-40rpx img-upload">
            <view class="img-upload-icon"></view>
          </view>
          <view class="text-c p-t-10rpx p-b-20rpx dark-color text-28rpx">上传资格证书</view>
        </wd-upload>
      </view>
      <view class="subText p-t-20rpx">备注：证书文字清晰、证书编号清晰</view>
    </view>

    <template #bottom>
      <view class="btn-fixed flex-c">
        <view
          v-if="isAdd === 'edit'"
          class="btn-delet m-r-30rpx"
          @click="delCancel"
          :class="isAdd === 'edit' ? 'w-30' : ''"
        >
          删除
        </view>
        <view class="btn_box" :class="isAdd === 'edit' ? 'w-70' : 'w-100'">
          <view class="btn_bg" @click="addSubmit">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { quickHandlers } from '@/utils/compressImage'
import { encryption } from '@/service/crypto'
import {
  resumeCertificateAdd,
  resumeCertificateUpdata,
  resumeCertificateDel,
} from '@/interPost/resume'
import { baseUrlImgCommon, baseUrlPrever } from '@/interPost/img'
import { useMessage } from 'wot-design-uni'
const { getToken } = useUserInfo()
const attachmentUrl = ref('')
const message = useMessage()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// before-upload 处理函数 - 使用预设的大尺寸压缩配置
const beforeUpload = quickHandlers.highQuality()
// 图片上传
const baseUrl = baseUrlImgCommon
// 简历id
const baseInfoId = ref(null)
// 图片id
const imgId = ref(null)
// 上传后的证书id
const attachmentId = ref(null)
const attachmentIdInit = ref(null) // 初始化
// 证书
const certificate = ref('')
const certificateInit = ref('') // 初始化
// 图片id
// 新增和编辑
const isAdd = ref(null)
const fileList = ref([])
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
//
onLoad(async (options) => {
  await nextTick()
  console.log(options, 'options===')
  baseInfoId.value = options.baseInfoId
  imgId.value = options.id
  certificate.value = options.certificate
  certificateInit.value = options.certificate // 备份
  isAdd.value = options.isAdd
  attachmentId.value = options.attachmentId
  attachmentIdInit.value = options.attachmentId // 备份
  attachmentUrl.value = options.attachmentUrl
  if (attachmentId.value && attachmentId.value !== undefined) {
    const imageUrl = baseUrlPrever + '/' + encryption(attachmentId.value)
    fileList.value = [
      {
        url: attachmentUrl.value,
        name: 'tupian',
      },
    ]
  }
})

// 图片上传成功
const successFun = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  // const res = JSON.parse(decryption(resJson.data))
  if (res.code === 0) {
    attachmentId.value = res.data[0].fileId
  }
}
// 删除
const delCancel = async () => {
  const res: any = await resumeCertificateDel({ id: imgId.value })
  if (res.code === 0) {
    uni.navigateBack()
  } else {
    uni.showToast({
      title: '请输入证书名称',
      icon: 'none',
      duration: 3000,
    })
  }
}
// 提交
const addSubmit = async () => {
  if (!certificate.value) {
    uni.showToast({
      title: '请输入证书名称',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (isAdd.value === 'add') {
    const res: any = await resumeCertificateAdd({
      certificate: certificate.value,
      attachmentId: attachmentId.value,
      baseInfoId: baseInfoId.value,
      status: 1,
    })
    if (res.code === 0) {
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  } else {
    const res: any = await resumeCertificateUpdata({
      certificate: certificate.value,
      attachmentId: attachmentId.value,
      baseInfoId: baseInfoId.value,
      status: 1,
      id: imgId.value,
    })
    if (res.code === 0) {
      uni.navigateBack()
    } else {
      uni.showToast({
        title: res.msg,
        icon: 'none',
        duration: 3000,
      })
    }
  }
}
// 返回
const back = () => {
  console.log(attachmentIdInit.value, 'attachmentIdInit.value=====')
  console.log(attachmentId.value, 'attachmentId.value=====')
  if (
    attachmentIdInit.value === attachmentId.value &&
    certificate.value === certificateInit.value
  ) {
    uni.navigateBack()
  } else {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        uni.navigateBack()
      })
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  text-align: right;
  background-color: transparent;
}
::v-deep .custom-class {
  font-size: 30rpx;
  color: #000;
  text-align: right;
}
::v-deep .custom-class-1 {
  display: flex;
  justify-content: center;
  width: 400rpx !important;
  height: 320rpx !important;
  padding: 20rpx 0rpx 20rpx;
  margin: auto;
}
::v-deep .wd-upload__preview {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  object-fit: contain !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
}

.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn-delet {
    display: flex;
    align-items: center;
    justify-content: center;
    // width: 30%;
    padding: 20rpx 0rpx;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    background: #959595;
    border-radius: 14px 14px 14px 14px;
  }
  .btn_box {
    box-sizing: border-box;
    // width: 70%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
.setting {
  padding: 40rpx 40rpx;

  .setting-card {
    padding: 20rpx 30rpx;
    background-color: #fff;
    border-radius: 20rpx;
    box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

    .img-upload {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 280rpx;
      height: 160rpx;
      margin: 40rpx auto 0rpx;
      background-color: #ededed;
      border-radius: 20rpx;

      .img-upload-icon {
        width: 96rpx;
        height: 96rpx;
        background-image: url('@/resumeRelated/img/zhengshuyanzhen_1.png');
        background-position: 100% 100%;
        background-size: 100% 100%;
      }
    }
  }
}
</style>
