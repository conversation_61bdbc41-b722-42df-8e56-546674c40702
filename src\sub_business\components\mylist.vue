<template>
  <view class="p-[34rpx_30rpx_0]" @tap="handleToPreviewDetail">
    <view class="flex flex-col gap-20rpx">
      <view class="flex items-center gap-26rpx">
        <wd-skeleton
          :row-col="[[{ width: '82rpx', height: '82rpx', type: 'circle' }]]"
          :loading="skeletonLoading"
        >
          <wd-img :src="hxUserInfo?.avatar || avatarOne" width="82rpx" height="82rpx" round />
        </wd-skeleton>
        <view class="flex flex-col gap-6rpx flex-1">
          <view class="c-#333333 flex items-center">
            <view class="flex-1 flex items-center">
              <text class="text-38rpx font-500">
                {{ hxUserInfo?.nickname }}
              </text>
              <view class="flex items-center" v-if="hxUserInfo?.isOnline">
                <view class="mx-8rpx size-6rpx bg-#34A715 rounded-full" />
                <text class="c-#34A715 text-24rpx">在线</text>
              </view>
            </view>
            <text class="c-#FF8080 text-32rpx font500">
              {{
                [
                  formatToKilo(personalList.salaryExpectationStart),
                  formatToKilo(personalList.salaryExpectationEnd),
                ]
                  .filter(Boolean)
                  .join('-') || '面议'
              }}
            </text>
          </view>
          <text class="c-#333333 text-24rpx line-clamp-1">
            {{
              [personOtherInfo.seekStatus, personOtherInfo.qualification, personalList.major]
                .filter(Boolean)
                .join(' | ')
            }}
          </text>
        </view>
      </view>
      <view class="flex flex-col gap-12rpx" v-if="personalList?.workExperienceList?.length">
        <view
          class="flex items-center gap-16rpx"
          v-for="(item, key) in (isWorkExperienceExpanded
            ? personalList?.workExperienceList
            : personalList?.workExperienceList?.slice(0, 1)) ?? []"
          :key="`workExperienceList-${key}`"
          @tap.stop="
            !key && personalList?.workExperienceList?.length > 1
              ? toggleWorkExperience()
              : undefined
          "
        >
          <wd-icon :name="postMark" size="26rpx" />
          <text class="c-#333333 text-26rpx flex-1 line-clamp-1">
            {{
              [
                item?.workCompanyName,
                item?.positionName,
                !(item?.workYears ?? 0) ? '1年内' : `${item?.workYears}年以上`,
              ]
                .filter(Boolean)
                .join(' · ')
            }}
          </text>
          <text
            v-if="!key && personalList?.workExperienceList?.length > 1"
            class="c-#000000 text-16rpx"
            :class="
              isWorkExperienceExpanded ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'
            "
          />
        </view>
      </view>
    </view>
    <view class="flex flex-col gap-22rpx mt-16rpx mb-24rpx">
      <text class="c-#333333 text-28rpx line-clamp-2" v-if="personalList?.myLights">
        {{ personalList.myLights }}
      </text>
      <view class="flex items-center flex-wrap gap-20rpx">
        <view
          class="bg-#F3F3F3 border-rd-6rpx h-46rpx min-w-150rpx px-20rpx center"
          v-for="(item, index) in splitToArray(personalList?.certificateNames)"
          :key="index"
        >
          <text class="c-#5D5B69 text-24rpx">{{ item }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { splitToArray, formatToKilo } from '@/utils'
import { DICT_IDS } from '@/enum'
import { UserInfoWithPresence } from '@/ChatUIKit/types'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'
import commonCard from './common-card.vue'
import avatarOne from '@/static/common/avatar/1.png'
import postMark from '@/static/common/post-mark.png'
import resumeMatching from '@/static/common/resume-matching.png'
import resumeCompetitiveness from '@/static/common/resume-competitiveness.png'
import resumeCompetitivenessStar from '@/static/common/resume-competitiveness-star.png'

interface propsInt {
  list: hrIndexResumeUserListInt
  position: hrPositionQueryOptionListInt
}
const props = withDefaults(defineProps<propsInt>(), {
  list: () => ({}) as hrIndexResumeUserListInt,
})
const { getDictLabel } = useDictionary()
const { sendGreetingMessage } = useIMConversation()
const { progressWidth, startProgress } = useProgress()
const { bool: animationBool, setTrue: setAnimationTrue, setFalse: setAnimationFalse } = useBoolean()
const {
  bool: skeletonLoading,
  setTrue: setSkeletonLoadingTrue,
  setFalse: setSkeletonLoadingFalse,
} = useBoolean()
const { bool: isWorkExperienceExpanded, toggle: toggleWorkExperience } = useBoolean()

const personOtherInfo = reactive({
  /** 学历 */
  qualification: '',
  /** 求职状态 */
  seekStatus: '',
})
const personalList = computed(() => props.list)
const displayActivityTotal = computed(() => {
  const total = personalList.value?.activityTotal
  if (!total) {
    return Math.floor(Math.random() * 101) + 100 // 100-200之间随机数
  }
  return total > 999 ? '999+' : total
})
const hxUserInfo = ref<Partial<UserInfoWithPresence>>({})
async function getHxUserInfo() {
  try {
    setSkeletonLoadingTrue()
    const { hxUserInfoVO } = personalList.value
    const { username } = hxUserInfoVO
    await uni.$UIKit.appUserStore.getUsersPresenceFromServer({
      userIdList: [username],
    })
    await uni.$UIKit.appUserStore.getUsersInfoFromServer({
      userIdList: [username],
    })
    const userInfo = uni.$UIKit.appUserStore.getUserInfoFromStore(username)
    hxUserInfo.value = userInfo
    setSkeletonLoadingFalse()
  } catch (error) {
    console.error('获取用户信息失败:', error)
  }
}
function useProgress() {
  const progressWidth = ref(0)
  const startProgress = () => {
    const randomProgress = Math.floor(Math.random() * 20) + 80
    setTimeout(() => {
      progressWidth.value = randomProgress
    }, 100)
  }
  return { progressWidth, startProgress }
}
function handleSendMessage() {
  try {
    const { id, positionName } = props.position
    const { hxUserInfoVO } = personalList.value
    const { username } = hxUserInfoVO
    sendGreetingMessage(username, {
      id,
      positionName,
      hxUserInfoVO,
    })
  } catch (error) {}
}
function handleToPreviewDetail() {
  const { userId } = personalList.value
  console.log(userId, 'userId==========')
  const hrDetailItem = JSON.stringify({
    userId,
  })
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/resumeRelated/preview/index', {
      hrDetailItem,
    }),
  })
}
async function getQualification() {
  personOtherInfo.qualification = (await getDictLabel(
    DICT_IDS.EDUCATION_REQUIREMENT,
    personalList.value.qualification,
  )) as string
}
async function getSeekStatus() {
  personOtherInfo.seekStatus = (await getDictLabel(
    DICT_IDS.SEEK_STATUS,
    personalList.value.seekStatus,
  )) as string
}
let animationTimer: number | null = null
let animationInterval: number | null = null

function startPlusOneAnimation() {
  if (animationTimer) clearTimeout(animationTimer)
  if (animationInterval) clearInterval(animationInterval)
  const triggerAnimation = () => {
    setAnimationTrue()
    animationTimer = setTimeout(() => {
      setAnimationFalse()
    }, 1500)
  }
  animationTimer = setTimeout(triggerAnimation, 1000)
  animationInterval = setInterval(triggerAnimation, 3000)
}

function stopPlusOneAnimation() {
  if (animationTimer) {
    clearTimeout(animationTimer)
    animationTimer = null
  }
  if (animationInterval) {
    clearInterval(animationInterval)
    animationInterval = null
  }
  setAnimationFalse()
}

onMounted(() => {
  getQualification()
  getSeekStatus()
  startProgress()
  startPlusOneAnimation()
  getHxUserInfo()
})

onUnmounted(() => {
  stopPlusOneAnimation()
})
</script>

<style lang="scss" scoped>
//
</style>
