<template>
  <view class="content">
    <z-paging
      ref="pagingRef"
      v-model="dataList"
      use-chat-record-mode
      safe-area-inset-bottom
      bottom-bg-color="#f8f8f8"
      empty-view-text="有什么可以帮忙的？"
      @query="queryList"
      @keyboardHeightChange="keyboardHeightChange"
      @hidedKeyboard="hidedKeyboard"
      :paging-style="pageStyle"
      empty-view-img="/static/img/deepSeek.png"
    >
      <template #top>
        <CustomNavBar>
          <template #left>
            <view class=""></view>
          </template>
          <template #content>
            <wd-img width="226rpx" height="52rpx" :src="deepseekTitle" class="m-t-10rpx"></wd-img>
          </template>
          <template #right>
            <image class="tabbarImg" src="/static/img/histryIcon.png"></image>
          </template>
        </CustomNavBar>
      </template>

      <view v-for="(item, index) in dataList" :key="index" style="position: relative">
        <view style="transform: scaleY(-1)">
          <chat-item :item="item"></chat-item>
        </view>
      </view>
      <template #bottom>
        <chat-input-bar :disabled="isAnswering" ref="inputBar" @send="doSend" />
        <customTabbar name="deepseek" />
      </template>
    </z-paging>
  </view>
</template>
<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import customTabbar from '@/components/common/custom-tabbar.vue'
import chatInputBar from '@/components/chat-input-bar/chat-input-bar.vue'
import chatItem from '@/components/chat-item/chat-item.vue'
import deepseekTitle from '@/static/img/deepseekTitle.png'
import { formatDateTime } from '@/utils/common'
import { historyList, chat } from '@/interPost/deepSeek'

defineOptions({
  name: 'DeepSeekPersonal',
})

// 初始化分页
const { pagingRef, pageInfo, pageData, pageStyle } = usePaging({
  style: {
    background: 'linear-gradient(125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 响应式数据
const params = ref({
  entity: {},
  orderBy: {},
  page: pageInfo.page,
  size: pageInfo.size,
})
const inputBar = ref(null)
const dataList = ref([])
const askMsg = ref('')
const isAnswering = ref(false)

// 查询历史记录
const queryList = async () => {
  await uni.$onLaunched
  const res: any = await historyList(params.value)
  pagingRef.value.complete(res.data.list)
}

// 生命周期
onLoad(async () => {
  await nextTick()
  pagingRef.value.reload()
})

// 键盘事件处理
const keyboardHeightChange = (res) => {
  inputBar.value.updateKeyboardHeightChange(res)
}

const hidedKeyboard = () => {
  inputBar.value.hidedKeyboard()
}

// 发送消息
const doSend = (msg) => {
  if (isAnswering.value) return

  askMsg.value = msg
  pagingRef.value.addChatRecordData({
    content: msg,
    icon: '/static/img/1.jpg',
    createTime: formatDateTime(),
    isMe: true,
    type: 0,
  })
  doAnswer()
}

// 核心修改：彻底清理响应文本
const cleanStreamResponse = (text) => {
  if (!text) return text

  // 1. 去除所有data:前缀（包括中间可能出现的）
  let cleaned = text.replace(/data:/g, '')

  // 2. 去除所有空白字符（包括空格、换行、制表符等）
  cleaned = cleaned.replace(/\s+/g, '')

  // 3. 特殊处理[DONE]标记（可选）
  cleaned = cleaned.replace(/\[DONE\]/g, '')

  return cleaned
}

// 流式输出处理
const streamTextAsync = async (text, callback, interval = 100) => {
  const cleanText = cleanStreamResponse(String(text))
  for (const char of cleanText) {
    callback(char)
    await new Promise((resolve) => setTimeout(resolve, interval))
  }
}

// 获取AI回答
const doAnswer = async () => {
  isAnswering.value = true
  try {
    // 添加"思考中"提示
    pagingRef.value.addChatRecordData({
      createTime: formatDateTime(),
      icon: '/static/img/deepseek2.png',
      content: '思考中...',
      isMe: false,
      type: 1,
    })

    // 获取API响应
    const res = await chat({ message: askMsg.value })
    if (!res) throw new Error('空响应')

    // 清理响应内容
    const cleanAnswer = cleanStreamResponse(res)
    askMsg.value = ''

    // 流式显示
    let currentAnswer = ''
    await streamTextAsync(cleanAnswer, (char) => {
      currentAnswer += char
      if (dataList.value.length > 0) {
        dataList.value[0].content = currentAnswer
      }
    })
  } catch (error) {
    console.error('回答出错:', error)
    if (dataList.value.length > 0) {
      dataList.value[0].content = '回答时出错，请稍后再试'
    }
  } finally {
    isAnswering.value = false
  }
}
</script>
<style scoped lang="scss">
.tabbarImg {
  width: 40rpx;
  height: 40rpx;
}
.header {
  padding: 20rpx;
  font-size: 20rpx;
  color: white;
  background-color: red;
}
.popup {
  position: absolute;
  top: -20px;
  z-index: 1000;
  width: 400rpx;
  height: 200rpx;
  background-color: red;
}
.start_photo {
  box-sizing: 220rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 200rpx;
  transform: rotate(180deg);
}
.start_deep {
  width: 340rpx;
  height: 340rpx;
}
</style>
