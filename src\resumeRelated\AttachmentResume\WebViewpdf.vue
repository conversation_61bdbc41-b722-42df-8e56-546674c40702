<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="简历预览" class="base_header"></CustomNavBar>
    </template>
    <!-- <view class="w-100">
      <web-view :src="link" :fullscreen="false"></web-view>
    </view> -->
    <!-- <pdf-viewer :src="pdfPath" style="width: 100%; height: 500px"></pdf-viewer> -->
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const pdfPath = ref('')
onLoad((options) => {
  const PluginName = uni.requireNativePlugin('pdf-viewer')
  console.log('PluginName:', PluginName)
  uni.downloadFile({
    url: options.fileUrl,
    success: (res) => {
      console.log('PDF下载结果:', res)
      if (res.statusCode === 200) {
        pdfPath.value = plus.io.convertLocalFileSystemURL(res.tempFilePath)
        console.log('PDF下载成功:', pdfPath.value)
      } else {
        uni.showToast({ title: '下载PDF失败', icon: 'none' })
      }
    },
    fail: () => {
      uni.showToast({ title: '下载PDF失败', icon: 'none' })
    },
  })
  console.log('options.link=====', options)
  // pdfPath.value = plus.io.convertLocalFileSystemURL('_www/test.pdf')
})
onMounted(() => {
  // const instance = getCurrentInstance()
  // const query = uni.createSelectorQuery().in(instance)
  // const { windowHeight } = uni.getSystemInfoSync() // 屏幕高度（单位：px）
  // console.log('屏幕高度:', windowHeight)
  // if (instance && instance.proxy) {
  //   const currentWebview = instance.proxy.$scope?.$getAppWebview()
  //   if (currentWebview) {
  //     nextTick(() => {
  //       setTimeout(() => {
  //         const closeHeight = 0
  //         let baseHeaderHeight = 0
  //         query
  //           .select('.base_header')
  //           .boundingClientRect((res) => {
  //             if (res && res.height) {
  //               baseHeaderHeight = res.height
  //             } else {
  //               baseHeaderHeight = 100 // 默认高度
  //             }
  //           })
  //           .exec(() => {
  //             const totalTop = closeHeight + baseHeaderHeight
  //             console.log('Calculated totalTop:', totalTop)
  //             const wv = currentWebview.children()?.[0]
  //             if (wv) {
  //               wv.setStyle({
  //                 top: `${totalTop}px`,
  //                 height: `${windowHeight - totalTop + 30}px`,
  //                 zIndex: -1,
  //               })
  //             }
  //           })
  //       }, 300)
  //     })
  //   }
  // }
})
</script>

<style lang="scss" scoped>
.box {
  padding: 40rpx 60rpx;
  .box-list {
    .box-list-item {
      padding: 30rpx 40rpx;
      background: #fff;
      border-radius: 30rpx;
      box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
    }
  }
}
</style>
