<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
    'app-plus': {
      softinputMode: 'adjustResize',
    },
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="项目业绩">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
        <template #right>
          <view @click="submit">确认</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-textarea
        clear-trigger="focus"
        size="workDescription "
        v-model="projectPerformance"
        :maxlength="1000"
        clearable
        show-word-limit
        focus
        custom-class="custom-class"
        custom-textarea-container-class="custom-textarea-container-class"
        custom-textarea-class="custom-textarea-class"
      />
    </view>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { useResumeStore } from '@/store'
import { useMessage } from 'wot-design-uni'
// 公司
const resumeStore = useResumeStore()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const projectPerformance = ref('')
// 初始化
const initNane = ref('')
const message = useMessage()
// 确认
const submit = () => {
  resumeStore.setProjectPerformance(projectPerformance.value)
  uni.navigateBack()
}
onLoad(async (options) => {
  await nextTick()
  projectPerformance.value = options.projectPerformance
  initNane.value = options.projectPerformance
})
// 返回
const back = () => {
  if (projectPerformance.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        resumeStore.setProjectPerformance(initNane.value)
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
.corporateName {
  padding: 40rpx 40rpx;
}
::v-deep .custom-class {
  border-radius: 20rpx !important;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}
::v-deep .custom-textarea-container-class {
  height: calc(100vh - 350rpx);
}
::v-deep .wd-textarea__inner {
  height: calc(100vh - 430rpx);
}
</style>
