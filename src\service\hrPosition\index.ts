import { POST, POSTPaging } from '../index'
import {
  hrPositionAddDataInt,
  hrPositionPayAndPublishDataInt,
  hrPositionQueryMyPublishListDataInt,
  hrPositionQueryMyPublishListInt,
  hrPositionQueryOptionListDataInt,
  hrPositionQueryOptionListInt,
} from './types'
import { HttpRequestConfig } from 'luch-request'

/** HR添加职位(入草稿)接口 */
export const hrPositionAdd = (data: hrPositionAddDataInt, config?: HttpRequestConfig) =>
  POST<number>('/easyzhipin-api/hrPosition/add', data, config)

/** 职位支付(预下单)及发布接口 */
export const hrPositionPayAndPublish = (
  data: hrPositionPayAndPublishDataInt,
  config?: HttpRequestConfig,
) => POST('/easyzhipin-api/hrPosition/payAndPublish', data, config)

/** 我发布的职位列表查询(根据不同的status查看)接口 */
export const hrPositionQueryMyPublishList = (
  data: Api.Request.IResPagingDataParamsInt<hrPositionQueryMyPublishListDataInt>,
  config?: HttpRequestConfig,
) =>
  POSTPaging<hrPositionQueryMyPublishListInt>(
    '/easyzhipin-api/hrPosition/queryMyPublishList',
    data,
    config,
  )

/** 职位下拉选数据查询接口 */
export const hrPositionQueryOptionList = (
  data: Api.Request.IResPagingDataParamsInt<hrPositionQueryOptionListDataInt>,
  config?: HttpRequestConfig,
) =>
  POSTPaging<hrPositionQueryOptionListInt>(
    '/easyzhipin-api/hrPosition/queryOptionList',
    data,
    config,
  )
