<template>
  <z-paging layout-only :paging-style="pageStyle" paging-class="business-paging">
    <template #top>
      <view
        class="h-510rpx relative px-30rpx bg-cover bg-center bg-no-repeat z--1"
        :style="{ backgroundImage: `url(${releasePost})` }"
      >
        <view
          class="absolute bottom-102rpx flex items-center h-76rpx w-296rpx rounded-38rpx bg-#2F2F2F px-36rpx"
          @tap="toggleSearch"
        >
          <view class="flex items-center gap-16rpx flex-1">
            <wd-img :src="releasePostSearch" width="42rpx" height="42rpx" />
            <text class="c-#E4CC9C text-28rpx">筛选条件</text>
          </view>
          <text
            class="text-16rpx c-#E4CC9C"
            :class="
              seniorSearchVisible ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'
            "
          />
        </view>
      </view>
    </template>
    <view class="relative h-full">
      <view
        class="transition-transform duration-300 ease-in-out h-full"
        :style="{ transform: `translateX(${seniorSearchVisible ? '-100%' : '0'})` }"
      >
        <component :is="seniorPost" ref="seniorPostRef" />
      </view>
      <view
        class="absolute top-0 left-0 size-full transition-transform duration-300 ease-in-out"
        :style="{ transform: `translateX(${seniorSearchVisible ? '0' : '100%'})` }"
      >
        <component
          :is="seniorSearch"
          v-model:show="selectPostPopupShowBool"
          @search="handleSeniorSearch"
          @select-post="handleSelectPost"
        />
      </view>
    </view>
    <template #bottom v-if="!seniorSearchVisible">
      <customTabbar name="deepseek" />
    </template>
  </z-paging>
  <wd-popup
    v-model="selectPostPopupShowBool"
    position="bottom"
    custom-class=" rounded-[20rpx_20rpx_0_0]"
  >
    <view class="h-700rpx">
      <z-paging
        ref="pagingRef"
        auto
        v-model="pageData"
        :fixed="false"
        :paging-style="pagePostStyle"
        @query="queryList"
        safe-area-inset-bottom
        :refresher-enabled="false"
        :show-loading-more-no-more-view="false"
      >
        <view class="flex flex-col gap-30rpx px-36rpx pt-30rpx">
          <view
            v-for="(item, key) in pageData"
            :key="key"
            class="px-34rpx py-14rpx bg-white shadow-[0rpx_20rpx_70rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx flex flex-col gap-10rpx"
            @click="handleSelectReleasePost(item)"
          >
            <text class="c-#555555 text-28rpx font-500">{{ item.positionName }}</text>
            <view class="grid grid-cols-3 gap-22rpx">
              <view
                v-for="(tag, index) in item.positionKeyList"
                :key="index"
                class="c-#888888 text-22rpx center h-36rpx bg-#F3F3F3 rounded-6rpx"
              >
                {{ tag }}
              </view>
            </view>
          </view>
        </view>
      </z-paging>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { useReleasePost } from '@/hooks/business/useReleasePost'
import { hrPositionQueryOptionList } from '@/service/hrPosition'
import type { hrResumeSeniorPostModelInt } from '@/service/hrResume/types'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'
import customTabbar from '@/components/common/custom-tabbar.vue'
import seniorPost from '@/components/deepseek/business/senior-post.vue'
import seniorSearch from '@/components/deepseek/business/senior-search.vue'
import releasePost from '@/static/deepseek/business/release-post.png'
import releasePostSearch from '@/static/deepseek/business/release-post-search.png'

defineOptions({
  name: 'DeepSeekBusiness',
})
const { releaseSeniorPostActivePost } = useReleasePost()
const { pageStyle } = usePaging({
  style: {
    background: '#383838',
  },
})
const {
  pagingRef,
  pageInfo,
  pageSetInfo,
  pageStyle: pagePostStyle,
  pageData,
} = usePaging<hrPositionQueryOptionListInt>({
  style: {
    borderRadius: '0 40rpx 40rpx 40rpx',
    background: '#ffffff',
  },
})
const {
  bool: seniorSearchVisible,
  setFalse: seniorSearchVisibleFalse,
  setTrue: seniorSearchVisibleTrue,
} = useBoolean()
const {
  bool: selectPostPopupShowBool,
  setTrue: selectPostPopupShowBoolTrue,
  setFalse: selectPostPopupShowBoolFalse,
} = useBoolean()
const seniorPostRef = ref<InstanceType<typeof seniorPost>>()
const toggleSearch = async () => {
  if (seniorSearchVisible.value) {
    seniorSearchVisibleFalse()
  } else {
    seniorSearchVisibleTrue()
  }
}
async function fetchPagingHrPositionQueryOptionList() {
  const { data } = await hrPositionQueryOptionList({
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}
async function fetchHrPositionQueryOptionList() {
  const { data } = await hrPositionQueryOptionList({
    page: 1,
    size: 1,
  })
  const { list } = data
  const [first] = list
  refreshHomeList(first)
}
async function refreshHomeList(post: hrPositionQueryOptionListInt) {
  releaseSeniorPostActivePost.value = post ?? {}
  seniorPostRef.value.reload()
}
function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  fetchPagingHrPositionQueryOptionList()
}
function handleSeniorSearch(model: hrResumeSeniorPostModelInt) {
  seniorSearchVisibleFalse()
  seniorPostRef.value.seniorPostReload(model)
}
async function handleSelectPost() {
  selectPostPopupShowBoolTrue()
}
function handleSelectReleasePost(post: hrPositionQueryOptionListInt) {
  refreshHomeList(post)
  selectPostPopupShowBoolFalse()
  toggleSearch()
}
onMounted(async () => {
  await uni.$onLaunched
  fetchHrPositionQueryOptionList()
})
</script>

<style lang="scss" scoped>
:deep(.wd-navbar__left) {
  align-items: end;
}

.business-paging {
  :deep(.zp-view-super) {
    margin: -60rpx 0 0;
  }
  :deep(.zp-paging-container-content) {
    height: 100%;
  }
}
</style>
