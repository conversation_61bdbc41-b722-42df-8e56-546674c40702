// export interface positionInfoQueryIMCardInfoByIdDataInt
//   extends Partial<Pick<Api.IM.UserBusinessExtInfo, 'companyId'>> {
//   /** 职位id */
//   positionId?: number
// }
export interface positionInfoQueryIMCardInfoByIdDataInt {
  hruserId: number
  userId: number
}
export interface positionInfoQueryIMCardInfoByIdInt {
  /** 城市名称 */
  cityName?: string
  /** 公司id */
  companyId?: number
  /** 公司名称 */
  companyName?: string
  /** 公司简称 */
  companyShortName?: string
  /** 区名称 */
  districtName?: string
  /** 职位id */
  positionInfoId?: number
  /** 职位福利list */
  positionBenefitList?: string[]
  /** 岗位名称 */
  positionName?: string
  /** 省份名称 */
  provinceName?: string
  /** 地址(不含省市区) */
  workLocation?: string
  /** 开始薪资 */
  workSalaryBegin?: number
  /** 结束薪资 */
  workSalaryEnd?: number
  /** 12薪资 *salaryMonths的值 */
  salaryMonths?: number
}

export interface positionInfoBatchSendPositionListDataInt {
  /** 城市code */
  cityCode: string
  /** 区code */
  districtCode?: string
  /** 职位code */
  positionCode: string
  /** 省份code */
  provinceCode: string
}

export interface positionInfoBatchSendPositionListInt
  extends Api.Common.Record<{
    /** 主键id */
    id: number
    /** 开始工资 */
    workSalaryBegin?: number
    /** 结束工资 */
    workSalaryEnd?: number
    /** 岗位名称 */
    positionName?: string
    /** 12薪资 *salaryMonths的值 */
    salaryMonths?: number
    /** 公司名字 */
    name?: string
    /** 区名称 */
    districtName?: string
  }> {}
