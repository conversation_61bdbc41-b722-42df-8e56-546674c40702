<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '道具',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          left-arrow
          :bordered="false"
          safe-area-inset-top
          custom-class="px-25rpx"
          @click-left="handleClickLeft"
        >
          <template #title>
            <text class="c-#333333 text-32rpx font500">我的道具</text>
          </template>
          <template #right>
            <text class="c-#333333 text-28rpx">记录</text>
          </template>
        </wd-navbar>
      </wd-config-provider>
    </template>
    <view class="flex flex-col gap-16rpx px-60rpx mt-44rpx">
      <propList />
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil, useToast, type ConfigProviderThemeVars } from 'wot-design-uni'
import { payPropQueryUsingCountList } from '@/service/payProp'
import { usePayProp } from '@/sub_business/hooks/usePayProp'
import propList from '@/sub_business/components/propList.vue'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const { payPropInfo, clearPayPropActive } = usePayProp()
const themeVars: ConfigProviderThemeVars = {
  navbarBackground: 'transparent',
  navbarArrowSize: '40rpx',
}
async function fetchPayPropQueryUsingCountList() {
  const { data } = await payPropQueryUsingCountList()
  payPropInfo.value = data
}
function handleClickLeft() {
  uni.navigateBack()
}
onMounted(async () => {
  await uni.$onLaunched
  fetchPayPropQueryUsingCountList()
})
onBeforeUnmount(() => {
  clearPayPropActive()
})
onShow(() => {
  clearPayPropActive()
})
</script>

<style lang="scss" scoped></style>
