<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘企业端用户规则</text>
        <text class="subtitle">版本：ver202505 生效日期：2025年05月24日</text>
      </view>

      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <text class="list-text">{{ item }}</text>
        </view>
      </view>

      <view class="content">
        <text class="paragraph">尊敬的企业用户：</text>
        <text class="paragraph">
          欢迎使用易直聘企业端服务。为了维护平台秩序，保障用户权益，请您仔细阅读并遵守以下用户规则。使用易直聘企业端服务即表示您同意遵守本规则的所有条款。
        </text>

        <view class="section" id="section-0">
          <text class="section-title">一、注册与认证规则</text>
          <text class="paragraph">1.企业资质要求</text>
          <text class="paragraph">
            1.1仅限合法注册的企业、个体工商户或机构使用，需提供营业执照、法人身份证等有效证明文件；
          </text>
          <text class="paragraph">
            1.2教育机构需提供办学许可证，人力资源服务机构需提供劳务派遣许可证等行业资质；
          </text>
          <text class="paragraph">1.3境外企业需提供当地注册证明及授权人身份信息。</text>
          <text class="paragraph">2.认证流程</text>
          <text class="paragraph">
            2.1提交资料后，平台将在1-3个工作日内完成人工审核；未通过需重新提交或补充材料。
          </text>
          <text class="paragraph">
            2.2企业信息变更（如名称、法人、地址）需在30日内更新资料并重新认证。
          </text>
          <text class="paragraph">3.账号权限</text>
          <text class="paragraph">
            3.1主账号由企业管理员持有，可创建子账号并分配权限（如发布职位、查看简历、沟通等）；
          </text>
          <text class="paragraph">3.2子账号需绑定员工真实姓名及联系方式，禁止共享账号。</text>
        </view>

        <view class="section" id="section-1">
          <text class="section-title">二、职位发布与管理规则</text>
          <text class="paragraph">1.发布权限</text>
          <text class="paragraph">
            仅认证企业可发布职位，可选择基础岗位和高级岗位发布；职位需明确分类（全职/兼职/实习）、薪资范围（禁止"面议"）、工作地点及职责描述。
          </text>
          <text class="paragraph">2.职位要求</text>
          <text class="paragraph">
            2.1禁止虚假信息、薪资误导、歧视性要求（如性别、年龄、地域）；
          </text>
          <text class="paragraph">2.2不得发布兼职刷单、传销、色情等违法岗位；</text>
          <text class="paragraph">2.3薪资范围不得超过实际标准的30%浮动；</text>
          <text class="paragraph">2.4职位需通过系统审核，违规内容将被驳回或下架。</text>
          <text class="paragraph">3.更新与编辑</text>
          <text class="paragraph">用户可以更新已发布职位，更新后职位原有权益不变；</text>
          <text class="paragraph">
            用户更新已发布职位过程中不得随意修改核心信息（如薪资、岗位类型）,如有修改，需重新审核，可能会导致职位驳回。
          </text>
        </view>

        <view class="section" id="section-2">
          <text class="section-title">三、人才沟通与互动规则</text>
          <text class="paragraph">1.主动沟通限制</text>
          <text class="paragraph">禁止发送骚扰、广告、虚假承诺内容，违规者将限制沟通功能。</text>
          <text class="paragraph">2.面试邀约</text>
          <text class="paragraph">2.1需明确面试时间、地点、联系人及方式。</text>
          <text class="paragraph">
            2.2求职者投诉面试流程不符，经核实后，可能对企业账号进行警告或岗位下架处理。
          </text>
          <text class="paragraph">3.人才库管理</text>
          <text class="paragraph">3.1可收藏求职者简历，禁止批量导出或外传。</text>
          <text class="paragraph">
            3.2人才标签需合规使用，禁止添加侮辱性、隐私性备注（如"已婚未育"）。
          </text>
        </view>

        <view class="section" id="section-3">
          <text class="section-title">四、隐私与数据安全</text>
          <text class="paragraph">1.信息保密</text>
          <text class="paragraph">
            1.1企业不得泄露求职者联系方式、身份证号等敏感信息，违者永久封号并承担法律责任。
          </text>
          <text class="paragraph">1.2简历下载需获得求职者授权同意并承诺不得对外传播。</text>
          <text class="paragraph">2.数据使用</text>
          <text class="paragraph">
            2.1企业仅可将平台数据用于招聘目的，禁止用于商业推广、倒卖等行为。
          </text>
          <text class="paragraph">2.2账号注销后，企业需在7日内删除本地存储的求职者数据。</text>
        </view>

        <view class="section" id="section-4">
          <text class="section-title">五、违规处理与争议解决</text>
          <text class="paragraph">1.违规行为界定</text>
          <text class="paragraph">
            1.1包括但不限于：虚假认证、职位欺诈、骚扰求职者、恶意竞争、数据滥用。
          </text>
          <text class="paragraph">1.2违规处罚分三级：提醒、警告、处罚。</text>
          <text class="paragraph">2.申诉机制</text>
          <text class="paragraph">
            企业对处罚有异议，可在3个工作日内通过客服提交申诉材料，平台在5个工作日内反馈结果。
          </text>
        </view>

        <view class="section" id="section-5">
          <text class="section-title">六、费用与服务说明</text>
          <text class="paragraph">1.付费服务</text>
          <text class="paragraph">1.1发布岗位、增值服务（职位置顶、急聘标识等）需单次付费。</text>
          <text class="paragraph">1.2费用支付后不支持退款（因平台故障除外）。</text>
          <text class="paragraph">2.发票与合同</text>
          <text class="paragraph">
            支付完成后可申请电子发票，申请人须提供注册账户对应的发票信息。
          </text>
        </view>

        <view class="section" id="section-6">
          <text class="section-title">七、免责条款</text>
          <text class="paragraph">
            企业需对自身发布的信息真实性负责，易直聘不承担因虚假信息导致的纠纷或损失。
          </text>
          <text class="paragraph">
            因系统维护、网络故障导致服务中断，易直聘不承担间接损失，但需提前24小时公告。
          </text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 目录列表
const contentList = ref([
  '一、注册与认证规则',
  '二、职位发布与管理规则',
  '三、人才沟通与互动规则',
  '四、隐私与数据安全',
  '五、违规处理与争议解决',
  '六、费用与服务说明',
  '七、免责条款',
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量
            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height // 导航栏高度 + 额外间距
            }
            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop
            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.content-list {
  padding: 20rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
}

.list-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.list-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;
  .list-text {
    flex: 1;
    font-size: 26rpx;
    color: #007aff;
  }
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.paragraph {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.footer {
  margin-top: 40rpx;
  text-align: right;
}

.footer-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
</style>
