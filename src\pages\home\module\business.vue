<template>
  <z-paging layout-only :paging-style="pageStyle" paging-class="business-paging">
    <template #top>
      <releasePost
        v-model:show="postSearchVisible"
        @select-post="handleSelectPost"
        @search-post="toggleSearch"
      />
    </template>
    <view class="relative h-full">
      <view
        class="transition-transform duration-300 ease-in-out h-full"
        :style="{ transform: `translateX(${postSearchVisible ? '-100%' : '0'})` }"
      >
        <component :is="releaseResume" ref="releaseResumeRef" />
      </view>
      <view
        class="absolute top-0 left-0 size-full transition-transform duration-300 ease-in-out"
        :style="{ transform: `translateX(${postSearchVisible ? '0' : '100%'})` }"
      >
        <component :is="releaseSearch" @select-post="handleSelectPost" />
      </view>
    </view>
    <template #bottom>
      <customTabbar name="home" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import customTabbar from '@/components/common/custom-tabbar.vue'
import releasePost from '@/components/home/<USER>/release-post.vue'
import releaseSearch from '@/components/home/<USER>/release-search.vue'
import releaseResume from '@/components/home/<USER>/release-resume.vue'
defineOptions({
  name: 'HomeBusiness',
})
const {
  bool: postSearchVisible,
  setFalse: postSearchVisibleFalse,
  setTrue: postSearchVisibleTrue,
} = useBoolean()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const releaseResumeRef = ref<InstanceType<typeof releaseResume>>()
const toggleSearch = async () => {
  if (postSearchVisible.value) {
    postSearchVisibleFalse()
  } else {
    postSearchVisibleTrue()
  }
}
function handleSelectPost() {
  releaseResumeRef.value?.reload()
  postSearchVisibleFalse()
}

const homeShow = () => {}

defineExpose({
  homeShow,
})
</script>

<style lang="scss" scoped>
.business-paging {
  :deep(.zp-paging-container-content) {
    height: 100%;
  }
}
</style>
