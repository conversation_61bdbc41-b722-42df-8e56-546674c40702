<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘增值服务协议</text>
        <text class="subtitle">版本：ver202505 生效日期：2025年05月24日</text>
      </view>

      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <text class="list-text">{{ item }}</text>
        </view>
      </view>

      <view class="content">
        <text class="paragraph">尊敬的用户：</text>
        <text class="paragraph">
          欢迎使用易直聘增值服务。为了保障您的权益，请仔细阅读本协议条款。点击"立即购买"或实际使用增值服务，即视为已阅读并同意本协议全部内容。
        </text>

        <view class="section" id="section-0">
          <text class="section-title">一、协议主体</text>
          <text class="paragraph">
            服务提供方：重庆中誉易职网络信息科技有限公司（以下简称"本公司"或"易直聘"）
          </text>
          <text class="paragraph">
            用户：指注册并使用易直聘APP及增值服务的个人或企业（以下简称"用户"）
          </text>
        </view>

        <view class="section" id="section-1">
          <text class="section-title">二、定义与范围</text>
          <text class="paragraph">
            1.增值服务：用户通过付费或参与活动获得的除基础招聘功能外的附加服务，包括但不限于：
          </text>
          <text class="paragraph">1.1极速置顶卡</text>
          <text class="paragraph">1.2曝光刷新卡</text>
          <text class="paragraph">1.3黑马炸弹卡</text>
          <text class="paragraph">1.4无限畅聊卡</text>
          <text class="paragraph">1.5简历下载次数扩容</text>
          <text class="paragraph">1.6其他定制化服务</text>
          <text class="paragraph">
            2.适用范围：本协议适用于用户通过易直聘平台或相关平台购买、使用增值服务的行为。
          </text>
        </view>

        <view class="section" id="section-2">
          <text class="section-title">三、服务内容与使用规则</text>
          <text class="paragraph">1.服务开通：</text>
          <text class="paragraph">
            1.1用户需完成实名认证（企业用户需提交营业执照）后方可购买增值服务。
          </text>
          <text class="paragraph">1.2部分服务可能需单独签订补充协议（如大型企业定制服务）。</text>
          <text class="paragraph">2.服务期限：</text>
          <text class="paragraph">2.1单次购买服务有效期以订单页面标注为准，逾期自动失效。</text>
          <text class="paragraph">2.2自动续费服务需用户主动授权，可随时关闭。</text>
          <text class="paragraph">3.使用限制：</text>
          <text class="paragraph">
            用户不得利用增值服务发布虚假职位、歧视性信息或违反法律法规的内容。
          </text>
        </view>

        <view class="section" id="section-3">
          <text class="section-title">四、费用与支付</text>
          <text class="paragraph">1.收费标准：</text>
          <text class="paragraph">
            1.1费用以平台内公示价格为准，本公司保留调整价格的权利（调整前7日公示）。
          </text>
          <text class="paragraph">1.2套餐类服务不可部分退款。</text>
          <text class="paragraph">2.支付方式：</text>
          <text class="paragraph">2.1支持微信支付、支付宝、企业银行转账。</text>
          <text class="paragraph">2.2企业用户可申请开具增值税发票（需提供开票信息）。</text>
          <text class="paragraph">3.争议处理：</text>
          <text class="paragraph">扣费异常需在3个工作日内提交凭证申诉，逾期视为无异议。</text>
        </view>

        <view class="section" id="section-4">
          <text class="section-title">五、数据与隐私保护</text>
          <text class="paragraph">1.数据使用：</text>
          <text class="paragraph">
            1.1用户通过增值服务获取的候选人简历仅限招聘用途，禁止转售、共享或用于商业爬虫。
          </text>
          <text class="paragraph">
            1.2企业用户行为数据（如搜索关键词、点击率）可能用于优化平台算法。
          </text>
          <text class="paragraph">2.安全措施：</text>
          <text class="paragraph">2.1采用SSL加密传输、定期安全审计。</text>
          <text class="paragraph">2.2用户需自行保管账号密码，泄漏导致的损失由用户承担。</text>
        </view>

        <view class="section" id="section-5">
          <text class="section-title">六、免责声明</text>
          <text class="paragraph">1.服务可用性：</text>
          <text class="paragraph">
            因系统维护、网络故障等导致服务中断的，本公司不承担赔偿责任，但应优先恢复服务。
          </text>
          <text class="paragraph">2.第三方责任：</text>
          <text class="paragraph">
            用户与候选人之间的纠纷由双方自行解决，本公司仅提供信息展示平台。
          </text>
          <text class="paragraph">3.不可抗力：</text>
          <text class="paragraph">包括但不限于自然灾害、政策变更、黑客攻击等导致的服务异常。</text>
        </view>

        <view class="section" id="section-6">
          <text class="section-title">七、协议变更与终止</text>
          <text class="paragraph">1.变更通知：</text>
          <text class="paragraph">
            协议修改后通过APP弹窗、站内信或邮件通知，用户继续使用视为接受新条款。
          </text>
          <text class="paragraph">2.终止情形：</text>
          <text class="paragraph">2.1用户账号被封禁，未使用的增值服务不予退款。</text>
          <text class="paragraph">
            2.2本公司因不可抗力停止运营，提前30日公告并安排剩余服务折现。
          </text>
          <text class="paragraph">3.争议解决</text>
          <text class="paragraph">
            因本协议产生的争议，双方应协商解决；协商不成，提交本公司所在地有管辖权的人民法院诉讼解决。
          </text>
        </view>

        <view class="section" id="section-7">
          <text class="section-title">八、确认条款</text>
          <text class="paragraph">
            用户点击"立即购买"或实际使用增值服务，即视为已阅读并同意本协议全部内容。
          </text>
          <text class="paragraph">
            注：本协议最终解释权归[重庆中誉易职网络信息科技有限公司]所有。
          </text>
        </view>

        <view class="footer">
          <text class="footer-text">生效日期：2025年05月24日</text>
          <text class="footer-text">版本：ver202505</text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

// 目录列表
const contentList = ref([
  '一、协议主体',
  '二、定义与范围',
  '三、服务内容与使用规则',
  '四、费用与支付',
  '五、数据与隐私保护',
  '六、免责声明',
  '七、协议变更与终止',
  '八、确认条款',
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量
            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height // 导航栏高度 + 额外间距
            }
            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop
            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.agreement-container {
  padding: 20rpx 40rpx;
}

.header {
  margin-bottom: 30rpx;
  text-align: center;
}

.title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
}

.content-list {
  padding: 20rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
}

.list-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333333;
}

.list-item {
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  margin-bottom: 10rpx;
  cursor: pointer;
  border-radius: 8rpx;
  .list-text {
    flex: 1;
    font-size: 26rpx;
    color: #007aff;
  }
}

.content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333;
}

.section {
  margin-bottom: 30rpx;
}

.section-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333333;
}

.paragraph {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  color: #333333;
  text-align: justify;
}

.footer {
  margin-top: 40rpx;
  text-align: right;
}

.footer-text {
  display: block;
  margin-bottom: 10rpx;
  font-size: 24rpx;
  color: #666666;
}
</style>
