<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar></CustomNavBar>
    </template>
    <view class="agreement-container">
      <view class="header">
        <text class="title">易直聘招聘行为管理规范</text>
      </view>
      <view class="protocol-meta">
        <text class="version">版本：ver202505</text>
        <text class="date">生效日期：2025年05月27日</text>
      </view>
      <view class="content-list">
        <view
          class="list-item"
          v-for="(item, index) in contentList"
          :key="index"
          @click="scrollToSection(index)"
        >
          <text class="list-text">{{ item }}</text>
        </view>
      </view>

      <text class="welcome-text">
        感谢您选择易直聘！为了构建一个严谨、真实的招聘与求职信息交流平台，确保为您提供及求职者更优质的服务体验，我们特此制定了易直聘平台的招聘行为规范。
      </text>

      <view
        class="section"
        v-for="(section, index) in sections"
        :key="'section' + index"
        :id="'section-' + index"
      >
        <view class="section-title article-title">{{ section.title }}</view>
        <text class="section-content">{{ section.content }}</text>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const contentList = ref([
  '一、前言',
  '二、原则',
  '三、违规行为的认定',
  '四、违规行为的类型',
  '五、违规细则',
  '六、管理措施',
  '七、通知',
  '八、其他',
  '九、定义',
])

const sections = ref([
  {
    title: '一、前言',
    content: `感谢您选择易直聘！为了构建一个严谨、真实的招聘与求职信息交流平台，确保为您提供及求职者更优质的服务体验，我们特此制定了易直聘平台的招聘行为规范。
1.《招聘行为管理规范》（以下简称"规范"）是用户（特指"招聘者"或有招聘需求的企业）与重庆中誉易职网络信息技术有限公司（以下简称"易直聘"、"平台"或"我们"）签订的《易直聘用户协议》的重要组成部分，与《易直聘用户协议》一同构成您与易直聘公司合作的法律文件。
2.在使用易直聘服务前，您必须仔细阅读并完全理解本规范。一旦您开始使用易直聘平台的服务，即意味着您已经阅读并同意遵守本规范的所有条款。
3.易直聘保留根据法律法规、政策以及产品需求更新本规范的权利，请您定期查看最新版本。若您在规范更新后继续使用易直聘服务，则表明您接受并同意了修订后的条款和内容。
4.本规范主要依据《中华人民共和国民法典》、《中华人民共和国网络安全法》、《中华人民共和国数据安全法》、《中华人民共和国个人信息保护法》、《中华人民共和国就业促进法》、《中华人民共和国劳动法》、《中华人民共和国劳动合同法》、《人力资源市场暂行条例》、《人力资源服务机构管理规定》、《互联网信息服务管理办法》、《网络招聘服务管理规定》、《网络信息内容生态治理规定》、《互联网用户账号名称管理规定》等国家法律法规及相关规范性文件（以下简称"法律规定"）制定。`,
  },
  {
    title: '二、原则',
    content: `1.用户仅限于招聘目的使用本平台，不得利用其从事任何违法或违规行为，包括但不限于推广营销、寻求合作等非招聘相关活动。
2.用户在进行招聘活动时，必须严格遵守相关法律法规及本规定，平台亦应遵守法律法规，并依据本规定对用户进行适当管理。`,
  },
  {
    title: '三、违规行为的认定',
    content: `1.违规行为认定的主要依据：
1.1监测审查的平台结果；
1.2行政管理部门的通报和通知；
1.3司法机关的法律文件；
1.4其他用户的投诉和举报；
1.5新闻媒体的曝光；
1.6其他合法渠道的反馈结果。
2.平台依据法律法规及本规范，结合证据材料对违规行为进行认定，并形成结果。平台依据此结果对用户实施管理措施。`,
  },
  {
    title: '四、违规行为的类型',
    content: `依据法律法规、社会公德、社区规范以及平台规章，平台将违规行为划分为以下二个类别：
1.违法违纪：用户行为触犯了法律明文禁止的条款，并可能对平台的招聘与求职安全产生重大负面影响，必须立即采取行动以防止影响进一步扩散。
2.损害求职体验：用户行为虽未违反法律明文禁止的条款，却严重损害了求职者的体验和平台的生态环境，或对招聘求职环境产生不利影响。`,
  },
  {
    title: '五、违规细则',
    content: `1.重大违法违规
1.1严禁危害国家安全、社会稳定类违法违规行为，包括但不限于：
1.1.1反对宪法基本原则的；
1.1.2危害国家安全、泄露国家秘密的；
1.1.3煽动颠覆国家政权、推翻社会主义制度、分裂国家、破坏国家统一的；
1.1.4损害国家荣誉和利益的；
1.1.5宣扬恐怖主义、极端主义的；
1.1.6分裂国家、民族、政治、疆土的；
1.1.7贬损、恶搞、损害革命领袖、英雄烈士人物形象、名誉的；
1.1.8不当使用党和国家领导人姓名、形象的；
1.1.9宣扬战争、买卖军火、交易枪支，如在职位发布、沟通聊天中提及上述敏感信息等；
1.1.10其他危害国家安全、社会稳定类违法信息，或有其他违反《网络信息内容生态治理规定》第六条、第七条规定的。

1.2禁止任何淫秽、色情的非法活动，具体包括：
1.2.1在平台使用过程中进行色情活动，例如在职位发布、交流对话、面对面面试时散布色情边缘内容，或有类似不当行为；
1.2.2发布涉及色情助理的职位信息，比如需要照顾日常生活、提供按摩服务、提供住家或贴身服务等；
1.2.3发布涉及色情主播的职位信息，如要求进行大尺度、边缘表演、穿着暴露或在直播中进行不雅、挑逗、性暗示、传播色情内容等；
1.2.4发布涉及色情技师的职位信息，如提供违法、色情、边缘服务等；
1.2.5发布涉及色情陪玩的职位信息，如以陪玩为名提供色情服务、一对一线下陪玩、同城旅游亲密陪玩陪睡等；发布专职陪酒的职位信息，如职位仅限女性且要求陪酒、工作内容为陪酒、专业陪酒、有偿一次性陪酒等；
1.2.6发布涉及色情模特的职位信息，如工作内容涉及色情或具有色情倾向、职位描述中展示大尺度、边缘内容或情趣内衣，或招聘者在介绍模特职位时有个人色情行为，如要求提供裸照、三围等；
1.2.7为夜场、花场或其他色情场所招聘，发布色情场所相关职位信息，如为色情场所代招、工作内容需要前往色情场所，或需要前往异地提供色情服务等；
1.2.8发布色情类违法信息，如卖淫、性交易、招嫖、外围或其他色情类服务信息等；
1.2.9发布其他淫秽色情类信息，或有类似行为的。

1.3严禁赌博类违法违规行为，包括但不限于：
1.4.1为赌场或其他涉赌企业招聘，发布与赌博相关职位，如工作内容包含从事赌博活动、担任荷官、发牌员， 或传播推广赌博相关信息等；
1.4.2为经营博彩或网络彩票业务的企业招聘，发布网络彩票售卖类职位。
1.5严禁发布毒品、精神药品、麻醉药品等违法违规信息。
1.6严禁在招聘过程中有暴力、人身攻击、伤害等行为。
1.7严禁宣扬封建迷信或发布包含黑社会、非法组织类违法违规信息，包括但不限于：
1.7.1发布封建迷信类违法违规信息；
1.7.2为涉黑企业或非法组织招聘，发布涉黑类职位。

1.8禁止一切诈骗及非法行为，具体包括：
1.8.1进行套路贷或其他形式的贷款诈骗，例如整容贷等；
1.8.2招募人员为从事刷单业务的公司工作，发布涉及刷单的职位信息，如职位介绍中含有兼职刷单、垫付返利等类似表述；
1.8.3进行金融诈骗活动，例如在职位描述中诱导求职者进行投资、成为交易员或分析师等职位无需经验即可加入，或包含其他类似内容；
1.8.4策划设局诈骗，如利用恋爱关系进行的杀猪盘诈骗、针对老年人的古董字画、股东分红或储值卡诈骗等；
1.8.5利用招聘名义，采取任何手段进行诈骗活动。

1.9严禁传销或传销模式企业招聘。

1.10禁止个人资料违规行为，具体包括：
1.10.1频繁异常地索要或骗取简历、微信、电话等联络方式，或通过未经平台授权的手段搜集个人信息，例如使用变种的联络方式来获取个人信息；
1.10.2出售账号，账号交易，以及账号或个人信息的买卖；
1.10.3在招聘过程中以任何不法手段收集个人信息。

1.11禁止违反保护未成年人规定的行为，具体包括：
1.11.1雇佣未满十六岁的儿童工作；
1.11.2让未成年人从事危险或被禁止的工作，例如地下作业、矿山等。

1.12禁止就业歧视行为，如因种族、肤色、宗教、性别、户籍、地域、年龄等不合理的因素对求职者进行区别对待。

1.13禁止其他违法业务或传播违法信息的行为，包括但不限于：
1.13.1发布挂靠资质的职位信息，如建筑、医疗等行业的资质挂靠；
1.13.2发布代孕、非法捐精/捐卵、性别选择、违反伦理的医学试验技术信息；
1.13.3发布征信修复信息；
1.13.4发布买卖公职信息；
1.13.5发布虚拟货币、虚拟数字资产信息；
1.13.6非法使用他人商标、品牌；
1.13.7非法使用他人肖像、作品，或利用平台发布信息从事侵权行为；
1.13.8发布代考、代写信息；
1.13.9利用平台规则漏洞谋取私利；
1.13.10发布网络水军信息；
1.13.11其他违法业务。

2.影响求职者体验
2.1反对所有不文明的行为，具体包括：
2.1.1言语放肆，以戏谑、无忌、不友好、嘲讽等方式声称从事违法活动；
2.1.2言语攻击，例如侮辱等；
2.1.3言语骚扰，如反复与明确拒绝的求职者交流、讨论无关话题等；
2.1.4面试失约；
2.1.5其他影响求职者体验的不文明行为。

2.2反对发布虚假职位信息，具体包括：
2.2.1发布职位类别不准确的职位信息，如为了获得更多曝光，恶意更改工作类型、错误选择职位分类、在面试沟通阶段才告知求职者实际招聘职位的不诚实行为等；
2.2.2以支付较低或不支付费用为目的，发布与实际招聘需求不一致或部分不一致的职位信息，包括但不限于错误选择职位类型、使用与实际招聘需求、岗位职责不匹配或容易引起误解的内容填写职位信息（包括但不限于职位/工作内容描述、关键词等）等不诚实行为；
2.2.3其他发布虚假职位信息的行为。
2.2.4反对发布任何不真实的企业信息，如伪造的logo、虚假业务、不实宣传、企业介绍不准确等。

3.不满足准入要求
3.1本平台不接受经营状况异常的企业或招聘者发布招聘信息，包括：企业营业执照已注销或吊销。
3.2本平台不接受设备异常的招聘者使用服务，包括：使用第三方插件，尤其是那些违规收集或存储求职者信息的。
3.3本平台不接受地址信息异常的招聘者使用服务，具体包括：
3.3.1提供虚假职位或面试地址，或隐瞒真实工作地点；
3.3.2职位定位在风险地区，如境外高风险地区或疑似色情、诈骗窝点的地址。

4.高风险职位
4.1提醒发布可能带来风险且需到外地工作的职位，包含但不限于：
4.1.1驻外类职位，例如前往境外风险地区、从事境外博彩业务或平台无法核实企业真实业务内容的情况等；
4.1.2出海类职位，如远洋捕捞、跟船出海等；
4.1.3其他具有高风险且需要到外地工作的职位。`,
  },
  {
    title: '六、管理措施',
    content: `根据违规行为的性质和严重性，平台将采取以下管理措施：
1.账号封禁措施：违规账号将受到封禁处理
1.1平台将依据违规行为的严重性和风险，决定具体的冻结方式，包括冻结招聘者身份、同时冻结招聘者和求职者身份等；
1.2若违规行为严重影响招聘求职安全，平台将依据法律法规要求，将违规用户提交的认证信息列入账号黑名单，并执行后续的管控措施。被列入黑名单的用户将无法使用平台服务。

2.内容驳回删除措施：违规内容将被驳回、删除或要求尽快修改
2.1平台将根据法律法规及本规范的规定，确定需驳回、删除的内容，如职位名称、职位描述、公司简介、薪资范围、敏感词等；
2.2用户需理解，平台有权对职位信息（包括但不限于用户选择的职位类型、填写的职位详情等）进行审核，若违反相关法律规定、平台规则或经平台提示后拒不修改的，平台有权进行职位驳回、限制发布、关闭已在线职位等措施。

3.对于多次违规或造成恶劣影响的用户，平台将依照本规范从重或加重处理。
4.若用户违规行为涉嫌违法犯罪，平台将依法向有关行政机关及司法部门举报、披露相关信息。
5.若用户违规行为给平台造成损失，平台将保留追究相应损害赔偿责任或违约责任的权利。`,
  },
  {
    title: '七、通知',
    content: `1.对于违反规定的用户，平台将利用站内通知、页面弹窗等多种途径，通知其处理结果。
2.若用户行为构成严重违法或违规，或需要立即实施限制措施以防止对招聘求职安全产生重大影响，平台有权限在发送通知的同时执行管理措施。`,
  },
  {
    title: '八、其他',
    content: `1.为确保易直聘平台的稳定与安全，所有用户禁止使用任何招聘管理系统、插件、外挂、工具或ATS，例如但不限于北森、大易、moka、图谱、云招、e成等，来访问或接入易直聘系统/使用易直聘的免费和付费服务（包括但不限于登录易直聘账号、发布职位、浏览职位、收发简历、筛选匹配、定向推送消息、自动沟通等）。
2.本平台保留根据法律法规、政策及产品需求更新本规范的权利，并将在平台内公布更新。平台将通过站内消息通知用户更新内容，以便用户掌握最新版本。`,
  },
  {
    title: '九、定义',
    content: `1."招聘者"：指的是那些拥有企业授权，可以在招聘平台上代表企业执行招聘任务的用户。这些用户在平台上的招聘活动应被视作其职责范围内的行为。
2."招聘活动"：指的是用户作为其雇主企业的代表，在招聘平台上进行的所有以招聘为目的的活动，这包括但不限于注册、登录、认证、发起对话、账号管理、发布职位信息、安排面试等。平台严格禁止用户利用招聘的名义，在平台上进行其他非招聘相关的行为。
3."人力资源服务机构"：指的是那些业务范围涵盖人力资源、劳务派遣，并且实际存在代招或从事人力资源、劳务派遣业务的招聘公司。`,
  },
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  console.log('点击定位到第', index, '个章节')

  const sectionId = `section-${index}`
  console.log('目标章节ID:', sectionId)

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      console.log('目标元素位置:', targetRect)

      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop
            console.log('计算滚动位置:', targetScrollTop)

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {
                    console.log('uni.pageScrollTo 成功')
                  },
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>

<style lang="scss" scoped>
.protocol-meta {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.welcome-text {
  display: block;
  margin-bottom: 30rpx;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}
.section-content {
  display: block;
  font-size: 28rpx;
  line-height: 1.8;
  color: #333;
  white-space: pre-line;
}
.version,
.date {
  font-size: 24rpx;
  color: #666;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.agreement-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx;
  margin-bottom: 120rpx;

  .header {
    margin-bottom: 20rpx;
    text-align: center;

    .title {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }

    .version,
    .date {
      display: block;
      font-size: 24rpx;
      line-height: 1.6;
      color: #999;
    }
  }

  .content-list {
    padding: 20rpx 0rpx;
    margin-bottom: 20rpx;
    border-radius: 12rpx;

    .content-title {
      display: block;
      margin-bottom: 15rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
    }

    .list-item {
      display: flex;
      align-items: flex-start;
      padding: 10rpx;
      margin-bottom: 10rpx;
      cursor: pointer;
      border-radius: 8rpx;

      .list-icon {
        margin-right: 10rpx;
        font-size: 24rpx;
      }

      .list-text {
        flex: 1;
        font-size: 26rpx;
        color: #007aff;
      }
    }
  }

  .content {
    flex: 1;
    padding: 20rpx;
    margin-bottom: 120rpx;
    border-radius: 12rpx;

    .section {
      margin-bottom: 30rpx;

      .section-title {
        display: block;
        margin-bottom: 15rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
      }
    }

    .footer {
      margin-top: 40rpx;
      text-align: center;

      .footer-text {
        font-size: 24rpx;
        color: #999;
      }
    }
  }

  .action-bar {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 100rpx;
    padding: 0 20rpx;

    .btn {
      width: 45%;
      height: 80rpx;
      margin: 0;
      font-size: 28rpx;
      line-height: 80rpx;
      border-radius: 40rpx;

      &.disagree {
        color: #666;
      }

      &.agree {
        color: #fff;
      }
    }
  }
}
</style>
