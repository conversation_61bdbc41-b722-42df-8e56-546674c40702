<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="地址中心"></CustomNavBar>
    </template>
    <view class="px-40rpx py-40rpx">
      <view class="px-40rpx py-20rpx bg-#fff rounded-[20rpx] flex justify-between items-center">
        <wd-textarea
          no-border
          readonly
          v-model="adreesName"
          custom-class="w-100"
          placeholder="定位公司地点"
          auto-height
          confirm-type="send"
          @confirm="confirm"
        />
        <view @click="geographicToAddressList">
          <view class="w-88rpx h-54rpx lh-54rpx text-center bg-#f0f0f0 rounded-[10rpx]">
            <wd-img :width="13" :height="13" :src="position" />
          </view>
        </view>
      </view>
      <view class="m-t-40rpx" v-if="imgMap">
        <image class="w-100 h-320rpx" :src="imgMap"></image>
      </view>
      <view class="m-t-40rpx px-60rpx py-20rpx bg-#fff rounded-[20rpx]">
        <wd-textarea
          no-border
          custom-class="w-100"
          v-model="fromData.address"
          placeholder="公司详细地址填写"
          auto-height
        />
      </view>
      <view class="m-t-40rpx px-40rpx py-20rpx bg-#fff rounded-[20rpx]">
        <view class="text-28rpx c-#000 m-b-30rpx">拍摄公司照片</view>
        <view class="flex items-center">
          <wd-upload
            custom-class="w-45 bg-#D9D9D9 h-200rpx flex items-center justify-center rounded-[10rpx] m-r-40rpx"
            reupload
            :before-upload="beforeUpload1"
            v-model:file-list="fileList1"
            image-mode="aspectFill"
            :limit="1"
            :header="header"
            :action="baseUrl"
            @success="successFun1"
            :sourceType="sourceType"
            accept="image"
          >
            <view class="w-100 text-center rounded-[10rpx] m-auto">
              <wd-img :width="20" :height="20" :src="upload"></wd-img>
              <view class="text-24rpx c-#000 m-t-10rpx">办公环境</view>
            </view>
          </wd-upload>
          <wd-upload
            custom-class="w-45 bg-#D9D9D9 h-200rpx flex items-center justify-center rounded-[10rpx] m-r-40rpx"
            reupload
            :before-upload="beforeUpload2"
            v-model:file-list="fileList2"
            image-mode="aspectFill"
            :limit="1"
            :header="header"
            :action="baseUrl"
            @success="successFun2"
            :sourceType="sourceType"
            accept="image"
          >
            <view class="w-100 text-center rounded-[10rpx] m-auto">
              <wd-img :width="20" :height="20" :src="upload"></wd-img>
              <view class="text-24rpx c-#000 m-t-10rpx">门头照片</view>
            </view>
          </wd-upload>
        </view>
        <view></view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="submit">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { hrCompanyWorkAddressAdd } from '@/service/companyAdress'
import { geographicToAddress, addressToGeographic } from '@/interPost/common'
import { queryKey } from '@/interPost/home'
import position from '@/setting/img/position.png'
import upload from '@/sub_business/static/release/upload.png'
import { quickHandlers } from '@/utils/compressImage'
const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 公司地址
const adreesName = ref('')
// 公司详细地址
const homeLocation = ref('')
const sourceType: any = ['camera']
// 参数
const fromData = ref({
  // 详细地址
  address: '',
  // 城市Code
  cityCode: '',
  // 城市名称
  cityName: '',
  // 区Code
  districtCode: '',
  //   区名称
  districtName: '',
  //   上传的图片id list
  imageIds: [],
  //   纬度
  lat: null,
  //   经度
  lon: null,
  //   省份Code ,
  provideCode: '',
  //   省份名称
  provideName: '',
})
const fileList1 = ref([])
const fileList2 = ref([])
// 图片id
const imgId1 = ref('')
const imgId2 = ref('')
// 图片1上传
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})
// before-upload 处理函数 - 使用预设的大尺寸压缩配置
const beforeUpload1 = quickHandlers.highQuality()
// before-upload 处理函数 - 使用预设的大尺寸压缩配置
const beforeUpload2 = quickHandlers.highQuality()
// 图片1上传成功
const successFun1 = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  if (res.code === 0) {
    imgId1.value = res.data[0].fileId
  }
}
// 图片2上传成功
const successFun2 = ({ fileList }) => {
  const res = JSON.parse(fileList[0].response)
  if (res.code === 0) {
    imgId2.value = res.data[0].fileId
  }
}
// 天地图图片
const imgMap = ref('')
// 天地图key
const staticKey = ref('')

const submit = async () => {
  if (!fromData.value.lat || !fromData.value.lon) {
    uni.showToast({
      title: '请检查定位，定位失败',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!imgId2.value || !imgId2.value) {
    uni.showToast({
      title: '请上传公司图片',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!fromData.value.address) {
    uni.showToast({
      title: '请输入详细地址',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await hrCompanyWorkAddressAdd({
    ...fromData.value,
    imageIds: [imgId1.value, imgId2.value],
  })
  if (res.code === 0) {
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}

// 获取ket
const getMapKet = async () => {
  const res: any = await queryKey()
  if (res.code === 0) {
    staticKey.value = res.data.staticKey
  }
}
// 获取经纬度
const getLocationInfo = () => {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: 'wgs84',
      success: function (res) {
        fromData.value.lat = res.latitude
        fromData.value.lon = res.longitude
        resolve(res)
      },
      fail: function (error) {
        uni.showToast({
          title: '获取定位失败',
          icon: 'none',
          duration: 3000,
        })
        reject(error)
      },
    })
  })
}
// 根据经纬度生成地址
const geographicToAddressList = async () => {
  // 如果没有经纬度，先获取位置

  try {
    await getLocationInfo()
  } catch (error) {
    console.error('获取位置失败:', error)
    return
  }

  // 确保有经纬度后再调用地址解析API
  if (!fromData.value.lat || !fromData.value.lon) {
    uni.showToast({
      title: '无法获取位置信息',
      icon: 'none',
      duration: 3000,
    })
    return
  }

  const res: any = await geographicToAddress({
    lat: fromData.value.lat,
    lon: fromData.value.lon,
    ver: '31',
  })
  if (res.code === 0) {
    adreesName.value = res.data.formatted_address ? res.data.formatted_address : ''
    fromData.value.lat = res.data.location.lat
    fromData.value.lon = res.data.location.lon
    fromData.value.cityCode = res.data.addressComponent.city_code
      ? res.data.addressComponent.city_code
      : res.data.addressComponent.province_code
    fromData.value.cityName = res.data.addressComponent.city
      ? res.data.addressComponent.city
      : res.data.addressComponent.province
    fromData.value.districtCode = res.data.addressComponent.county_code
    fromData.value.districtName = res.data.addressComponent.county
    fromData.value.provideCode = res.data.addressComponent.province_code
    fromData.value.provideName = res.data.addressComponent.province
    if (fromData.value.lat && fromData.value.lon) {
      imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${fromData.value.lon},${fromData.value.lat}&width=300&height=200&zoom=12&tk=${staticKey.value}&markers=${fromData.value.lon},${fromData.value.lat}`
    }
  }
}
// d
const confirm = async () => {
  const res: any = await addressToGeographic({ address: adreesName.value })
  console.log(res, '地址换经纬度')
  if (res.code === 0) {
    adreesName.value = res.data.keyWord ? res.data.keyWord : ''

    fromData.value.lat = res.data.lat
    fromData.value.lon = res.data.lon
    if (fromData.value.lat && fromData.value.lon) {
      imgMap.value = `https://api.tianditu.gov.cn/staticimage?center=${fromData.value.lon},${fromData.value.lat}&width=300&height=200&zoom=12&tk=${staticKey.value}&markers=${fromData.value.lon},${fromData.value.lat}`
    }
  } else {
    uni.showToast({
      title: '获取定位失败',
      icon: 'none',
      duration: 3000,
    })
  }
}
onLoad(async (options) => {
  await getMapKet()
  // 页面加载时获取位置并解析地址
  await geographicToAddressList()
})
</script>

<style scoped lang="scss">
:deep(.wd-upload__preview) {
  width: 100% !important;
  height: 100% !important;
  margin: 0rpx !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 24rpx 0rpx rgba(0, 0, 0, 0.1);
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 25rpx 0rpx;
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
