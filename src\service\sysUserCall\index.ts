import { POST } from '../index'
import { sysUserCallQueryOneDataInt, sysUserCallUpdateDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 查看自己的招呼语 */
export const sysUserCallQueryOne = (data: sysUserCallQueryOneDataInt, config?: HttpRequestConfig) =>
  POST<string>('/easyzhipin-api/sysUserCall/queryOne', data, config)
/** 更新自己的招呼语 */
export const sysUserCallUpdate = (data: sysUserCallUpdateDataInt, config?: HttpRequestConfig) =>
  POST('/easyzhipin-api/sysUserCall/update', data, config)
