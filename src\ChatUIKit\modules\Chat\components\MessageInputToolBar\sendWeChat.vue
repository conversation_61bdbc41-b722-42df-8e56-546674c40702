<template>
  <view class="flex justify-center" @tap="handleSendWeChat">
    <ItemContainer title="换微信" :icon-url="wxImg" />
  </view>
</template>

<script lang="ts" setup>
import { autorun } from 'mobx'
import { myQueryMyInfo } from '@/service/my'
import type { InputToolbarEvent } from '@/ChatUIKit/types/index'
import ItemContainer from './itemContainer.vue'
import wxImg from '@/ChatUIKit/static/message-custom/wx-white.png'

const toolbarInject = inject<InputToolbarEvent>('InputToolbarEvent')
const { sendCustomWeChatCodeMessage, getIMUserInfo, customCardInfo, getCoversationInfo } =
  useIMConversation()
const extUserInfo = ref<Api.IM.UserBusinessExtInfo>({})

async function handleSendWeChat() {
  const { conversationId } = getCoversationInfo.value
  if (!conversationId) {
    uni.showToast({
      title: '请稍后重试',
      icon: 'none',
    })
    return
  }
  const {
    data: { wxCode },
  } = await myQueryMyInfo()
  if (!wxCode) {
    uni.showToast({
      title: '请先设置微信号后再交换',
      icon: 'none',
    })
    // message
    //   .confirm({
    //     title: '提示',
    //     msg: '请先设置微信号后再交换',
    //   })
    //   .then(() => {
    //     uni.navigateTo({
    //       url: '/setting/wxUpdata/index',
    //     })
    //   })
    //   .catch()
    return
  }
  sendCustomWeChatCodeMessage(conversationId, {
    wechatCode: wxCode,
    userId: extUserInfo.value.hrUserId,
  })
  toolbarInject?.closeToolbar()
}

const unwatchUserInfo = autorun(() => {
  const { conversationId, conversationType } = getCoversationInfo.value
  if (conversationType === 'singleChat') {
    const { ext } = getIMUserInfo(conversationId)
    extUserInfo.value = JSON.parse(ext || '{}') as Api.IM.UserBusinessExtInfo
  }
})
onUnmounted(() => {
  unwatchUserInfo()
})
</script>

<style lang="scss" scoped>
//
</style>
