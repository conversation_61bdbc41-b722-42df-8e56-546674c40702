<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title=""></CustomNavBar>
    </template>

    <view class="protocol-container">
      <!-- 协议头部 -->
      <view class="protocol-header">
        <text class="title">易直聘人力资源协议</text>
        <view class="meta-info">
          <text class="version">版本：ver202505</text>
          <text class="date">生效日期：2025年05月24日</text>
        </view>
      </view>

      <!-- 目录导航 -->
      <view class="content-nav">
        <view class="nav-list">
          <view
            class="nav-item"
            v-for="(item, index) in navItems"
            :key="index"
            @click="scrollToSection(index)"
          >
            <!-- <text class="nav-icon">•</text> -->
            <text class="nav-text">{{ item }}</text>
          </view>
        </view>
      </view>

      <!-- 协议正文 -->
      <!-- 第一条 -->
      <view class="article" id="section-0">
        <text class="article-title">一、人力资源机构准入条件</text>
        <view class="clause">
          <text class="clause-title">1.资质要求</text>
          <text class="clause-text">
            1.1需提供有效的《人力资源服务许可证》或《劳务派遣许可证》（如涉及派遣业务）;
          </text>
          <text class="clause-text">1.2营业执照经营范围需包含"职业中介"或"人力资源服务";</text>
          <text class="clause-text">1.3境外猎头公司需额外提供合法经营证明。</text>
        </view>
        <view class="clause">
          <text class="clause-title">2.合规审核</text>
          <text class="clause-text">
            易直聘有权对机构信用记录及风险进行背调，黑名单机构（如曾有劳务纠纷、诈骗记录）禁止入驻。
          </text>
        </view>
      </view>

      <!-- 第二条 -->
      <view class="article" id="section-1">
        <text class="article-title">二、合作模式与权限</text>
        <view class="clause">
          <text class="clause-title">1.账号权限</text>
          <text class="clause-text">
            1.1人力资源机构账号可发布"外包岗位"或"代招职位"，但需明确标注雇主企业名称及合作关系；
          </text>
          <text class="clause-text">1.2禁止以雇主企业名义直接招聘（需获得雇主书面授权）。</text>
        </view>
        <view class="clause">
          <text class="clause-title">2.人才推荐规则</text>
          <text class="clause-text">
            推荐的求职者简历需获得其本人授权，不得买卖、转让简历数据；
          </text>
          <text class="clause-text">成功推荐入职后，需按平台规则结算服务费。</text>
        </view>
      </view>

      <!-- 第三条 -->
      <view class="article" id="section-2">
        <text class="article-title">三、费用与结算</text>
        <view class="clause">
          <text class="clause-title">1.平台服务费</text>
          <text class="clause-text">
            通常按职位发布数量、推荐成功人数或年度合作套餐收费，具体费率以合同约定为准。
          </text>
        </view>
        <view class="clause">
          <text class="clause-title">2.佣金结算</text>
          <text class="clause-text">
            企业与人力资源机构之间的佣金结算由双方自行约定，易直聘不参与分成（除非平台明确提供担保交易服务）。
          </text>
        </view>
      </view>

      <!-- 第四条 -->
      <view class="article" id="section-3">
        <text class="article-title">四、数据与隐私保护</text>
        <view class="clause">
          <text class="clause-title">1.信息使用限制</text>
          <text class="clause-text">
            1.1仅可将求职者信息用于本次招聘目的，禁止将简历数据用于其他商业用途（如电话营销）。
          </text>
          <text class="clause-text">
            1.2必须遵守《个人信息保护法》，离职求职者的信息需及时删除。
          </text>
        </view>
        <view class="clause">
          <text class="clause-title">2.平台监管</text>
          <text class="clause-text">
            易直聘有权巡查人力资源机构的招聘行为，发现违规可暂停账号并追责。
          </text>
        </view>
      </view>

      <!-- 第五条 -->
      <view class="article" id="section-4">
        <text class="article-title">五、违规与处罚</text>
        <view class="clause">
          <text class="clause-title">1.禁止行为</text>
          <text class="clause-text">1.1虚构岗位、隐瞒真实雇主信息。</text>
          <text class="clause-text">1.2向求职者收取费用（如报名费、培训费）。</text>
          <text class="clause-text">1.3恶意挖角其他企业已在职员工（需遵守竞业限制）。</text>
        </view>
        <view class="clause">
          <text class="clause-title">2.处罚措施</text>
          <text class="clause-text">2.1首次违规：警告并下架相关职位。</text>
          <text class="clause-text">
            2.2多次违规：冻结账号余额、终止合作，并列入行业共享黑名单。
          </text>
        </view>
      </view>

      <!-- 第六条 -->
      <view class="article" id="section-5">
        <text class="article-title">六、协议终止</text>
        <view class="clause">
          <text class="clause-title">1.主动退出</text>
          <text class="clause-text">需提前30天书面通知，并结清所有费用。</text>
        </view>
        <view class="clause">
          <text class="clause-title">2.平台终止权</text>
          <text class="clause-text">
            若机构涉嫌违法或重大违约，易直聘可立即终止服务并保留索赔权利。
          </text>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'

// 添加 z-paging 引用
const pagingRef = ref(null)

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const navItems = ref([
  '一、人力资源机构准入条件',
  '二、合作模式与权限',
  '三、费用与结算',
  '四、数据与隐私保护',
  '五、违规与处罚',
  '六、协议终止',
])

// 直接定位到指定章节 (适配 z-paging)
const scrollToSection = (index: number) => {
  const sectionId = `section-${index}`

  // 在 z-paging 组件中，需要使用组件内部的查询方法
  const query = uni.createSelectorQuery().in(pagingRef.value)

  // 获取目标元素的位置
  query
    .select(`#${sectionId}`)
    .boundingClientRect((targetRect) => {
      if (targetRect && !Array.isArray(targetRect)) {
        // 获取导航栏高度
        const navQuery = uni.createSelectorQuery()
        navQuery
          .select('.customNavbar')
          .boundingClientRect((navBarRect) => {
            let offsetTop = 150 // 默认偏移量

            if (navBarRect && !Array.isArray(navBarRect)) {
              offsetTop = navBarRect.height
            }

            // 计算目标滚动位置
            const targetScrollTop = targetRect.top - offsetTop

            // 使用 z-paging 的滚动方法
            if (pagingRef.value) {
              try {
                // 方法1: 尝试使用 z-paging 的 scrollToY 方法
                pagingRef.value.scrollToY(Math.max(0, targetScrollTop), false)
              } catch (error) {
                // 方法2: 尝试使用 uni.pageScrollTo
                uni.pageScrollTo({
                  scrollTop: Math.max(0, targetScrollTop),
                  duration: 0,
                  success: () => {},
                })
              }
            }
          })
          .exec()
      }
    })
    .exec()
}
</script>
<style scoped lang="scss">
.setting {
  padding: 0rpx 40rpx;
  .setting-list {
    padding: 30rpx 20rpx;
  }
}
.btn-flexd-red {
  height: 80rpx;
  line-height: 80rpx;
  color: #fff;
  text-align: center;
  background-color: #ff8080;
  border-radius: 30rpx;
}

.btn-flexd {
  position: fixed;
  bottom: 80rpx;
  left: 15%;
  width: 70%;
  margin: auto;
}

::v-deep .u-button {
  height: 80rpx;
  border-radius: 30rpx;
}

::v-deep .u-button__text {
  font-size: 28rpx !important;
  font-weight: bold;
  color: #fff !important;
}
.protocol-container {
  display: flex;
  flex-direction: column;
  padding: 20rpx 40rpx;
}

.protocol-header {
  padding: 20rpx;
  text-align: center;
  border-radius: 12rpx;
}

.title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.meta-info {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}

.version,
.date {
  font-size: 24rpx;
  color: #666;
}

.content-nav {
  padding: 20rpx;
  border-radius: 12rpx;
}

.nav-title {
  display: block;
  margin-bottom: 15rpx;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}
.nav-item {
  display: flex;
  align-items: center;
  padding: 10rpx;
  //   width: 50%;
  margin-bottom: 12rpx;
  cursor: pointer;
  border-radius: 8rpx;
}

.nav-icon {
  margin-right: 10rpx;
}

.nav-text {
  font-size: 26rpx;
  color: #007aff;
}

.protocol-content {
  flex: 1;
  padding: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
}

.article-title {
  display: block;
  margin-bottom: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
}

.clause {
  margin-bottom: 20rpx;
}

.clause-title {
  display: block;
  margin-bottom: 10rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #333;
}

.clause-text {
  display: block;
  padding-left: 20rpx;
  margin-bottom: 8rpx;
  font-size: 26rpx;
  line-height: 1.8;
  color: #333;
}

.action-buttons {
  position: sticky;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  padding: 20rpx 0;
}

.confirm-btn,
.cancel-btn {
  width: 48%;
  height: 80rpx;
  font-size: 28rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
}

.confirm-btn {
  color: #fff;
}

.cancel-btn {
  color: #666;
}
</style>
