<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
    'app-plus': {
      //手机软键盘升起不让其将页面头部上推
      softinputMode: 'adjustResize',
    },
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar>
        <template #right>
          <wd-img
            :src="identitySwitchingImg"
            width="45rpx"
            height="45rpx"
            @click="changeIdentFun"
          />
        </template>
      </CustomNavBar>
    </template>

    <view class="page_box">
      <view class="page_padding">
        <view class="page_flex_row">
          <view class="page_flex">
            <view class="page_flex_left">身份创建</view>
            <view class="page_flex_icon">
              <view class="page_flex_img"></view>
            </view>
          </view>
          <view class="page_input text-wrap">
            在线简历将会向企业展示,我们会妥善保护您的隐私,后续您也可以在设置中将简历隐藏～
          </view>
        </view>
        <view class="page_flex_row_bottom flex items-center">
          <view class="tag-name w-100rpx">姓名</view>
          <view class="input-border w-100">
            <wd-input
              type="text"
              :no-border="true"
              v-model="formData.trueName"
              placeholder="请输入您的真实姓名"
              :adjust-position="false"
              :focus="false"
            />
          </view>
        </view>
        <view class="w-100 flex items-center p-t-0rpx">
          <view class="tag-name w-100rpx">性别</view>
          <view class="tag-select-r-list w-100">
            <view
              @click="changeactive(index, item)"
              class="tag-select-r"
              :class="
                index === activeIndex && formData.sex === 1
                  ? 'myStyle-box'
                  : index === activeIndex && formData.sex === 2
                    ? 'myStyle-box-pink'
                    : 'tag-select-r-normal'
              "
              v-for="(item, index) in sexList"
              :key="index"
            >
              {{ item.name }}
            </view>
          </view>
        </view>
        <view class="page_flex_row_bottom flex items-center">
          <view class="tag-name w-200rpx">出生年月</view>
          <view class="month-picker w-100">
            <!-- <month-picker
              mode="date"
              :value="formData.birthday"
              class="picker"
              @change="handlePickerChange"
            ></month-picker> -->
            <wd-datetime-picker
              v-model="formData.birthday"
              label=""
              :default-value="defaultBirthday"
              type="year-month"
              @confirm="handlePickerChange"
              :minDate="minDate"
              :maxDate="maxDate"
              placeholder="请选择出生年月"
            />
          </view>
        </view>
        <view class="page_flex_row_bottom flex items-center">
          <view class="tag-name w-200rpx">求职状态</view>
          <view class="tag-select-r-list w-100">
            <wd-picker
              custom-view-class="custom-view-class"
              :columns="qzList"
              label=""
              v-model="formData.seekStatus"
            />
            <!-- <view
              @click="changeactiveTwo(index, item)"
              class="tag-select-r-x text-28rpx p-t-20rpx p-b-20rpx"
              :style="index === 2 ? 'width:560rpx' : ''"
              :class="index === activeIndexTwo ? 'myStyle-box text-28rpx' : ''"
              v-for="(item, index) in qzList"
              :key="index"
            >
              {{ item.name }}
            </view> -->
          </view>
        </view>
        <view class="page_flex_row_bottom">
          <view class="tag-name">个人亮点</view>
          <wd-textarea
            clear-trigger="focus"
            :adjust-position="false"
            :focus="false"
            v-model="formData.myLights"
            clearable
            placeholder="请填写您的自我介绍，让企业更快的了解您"
            custom-class="custom-class"
            custom-textarea-container-class="custom-textarea-container-class"
            custom-textarea-class="custom-textarea-class"
          />
        </view>
      </view>
    </view>

    <view class="btn_fixed" @click="submit">
      <view class="btn_box">
        <view class="btn_bg">下一步</view>
      </view>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import monthPicker from '@/components/month-picker/month-picker.vue'
import { userResumeBaseInfo, resumeBaseInfoList } from '@/interPost/biographical'
import { getStatusBar, getCheackInfo } from '@/utils/storage'
import { formatTime } from '@/utils/common'
import identitySwitchingImg from '@/static/mine/business/identity-switching.png'
import { DICT_IDS } from '@/enum'
import { randomInt } from '@/utils/util'
const { changeIdent } = useChangeIdent()
const { getDictData } = useDictionary()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const formData = reactive({
  trueName: '',
  sex: 1,
  // 初始化为null，onLoad后赋值为时间戳
  birthday: null,
  seekStatus: 0,
  id: null,
  myLights:
    '我性格活泼开朗，待人真诚热情，对待工作认真负责，具备较强的责任心和执行力。学习能力强，能快速掌握新知识技能，与同事相处融洽，具备良好的团队合作精神和集体荣誉感。',
})

const activeIndex = ref(0)
const statusBarHeight = ref(0)
const sexList = reactive([
  {
    name: '男',
    value: 1,
  },
  {
    name: '女',
    value: 2,
  },
])
// 个人亮点
const handHighlight = reactive([
  {
    content:
      '我性格开朗乐观，沟通表达能力强，善于建立良好人际关系。工作积极主动，责任心强，执行力出色。乐于学习新知识，适应能力强，能快速融入团队并高效协作，以达成共同目标为荣。',
  },
  {
    content:
      '本人真诚友善，乐于助人，具备良好的亲和力。做事踏实细致，有强烈的责任感和敬业精神。学习意愿强烈，善于钻研，团队意识强，注重配合，相信集体力量大于个人。',
  },
  {
    content:
      '我热情外向，思维活跃，具备良好的沟通协调能力。对待工作严谨认真，目标明确，执行力强。乐于接受挑战，学习新技能速度快，团队荣誉感强，认同集体价值。',
  },
  {
    content:
      '本人沉稳踏实，耐心细致，具备较强的抗压能力。工作认真负责，追求高效，注重细节和结果。持续学习提升自我，适应变化灵活，团队协作顺畅，以团队成功为己任。',
  },
  {
    content:
      '我积极进取，充满活力，沟通能力出色。工作态度端正，勇于承担责任，行动力强。具备快速学习与应用能力，与同事关系融洽，注重集体荣誉，团队协调能力强。',
  },
  {
    content:
      '本人性格随和，易于相处，具备良好的人际交往技巧。工作勤奋务实，责任心强，学习能力强，能较快适应新环境，团队合作精神突出，积极参与团队建设。',
  },
  {
    content:
      '我自信乐观，表达清晰，擅长与人沟通合作。工作积极主动，认真负责，效率优先。具备较强的学习能力和实践能力，能迅速适应新环境，是可靠的团队伙伴，重视团队目标。',
  },
  {
    content:
      '本人富有同理心，待人真诚，善于倾听与理解。工作细致专注，有担当，执行力可靠。能快速掌握新知识新方法，团队融入度高，协作意识强，维护集体利益。',
  },
  {
    content:
      '我条理清晰，逻辑性强，具备良好的分析与解决问题能力。工作专注投入，责任心强，注重时效。乐于学习新领域知识，适应性强，在团队中能有效沟通协作，贡献力量。',
  },
  {
    content:
      '本人亲和力强，乐于沟通，具备良好的服务意识。工作认真负责，一丝不苟，执行力强。学习新事物积极主动，上手快，注重团队配合，集体荣誉感强。',
  },
  {
    content:
      '我思维严谨，逻辑清晰，对技术充满热情。工作专注高效，追求卓越，责任心强。具备扎实的学习能力和问题解决能力，善于在团队中交流技术方案，协作攻关。',
  },
  {
    content:
      '本人思维活跃，富有创意和想象力，乐于探索新事物。工作充满激情，认真负责，执行力强。学习能力强，能快速吸收新知识并应用，在团队中善于激发灵感，协作共创。',
  },
  {
    content:
      '我目标明确，组织协调能力强，善于统筹规划。工作认真负责，注重结果，执行力突出。具备快速学习能力和适应能力，擅长团队建设与资源整合，推动项目达成。',
  },
  {
    content:
      '本人热情外向，亲和力强，沟通能力出色。工作积极主动，服务意识强，执行力高效。学习新知识快，应变能力强，注重团队协作，共同提升客户满意度。',
  },
  {
    content:
      '我勤奋好学，积极向上，充满工作热情。态度认真负责，积极主动完成任务。学习能力和适应能力强，渴望快速成长，乐于融入团队，虚心协作，贡献所学',
  },
  {
    content:
      '本人性格沉稳，做事有条理，值得信赖。工作认真细致，责任心极强，执行力可靠。具备持续学习意愿和能力，适应环境变化，是团队中稳定协作的力量',
  },
  {
    content:
      '我注重效率，行动迅速，目标感清晰。工作认真投入，责任心强，执行力出色。学习新方法快，能快速适应新要求，在团队协作中注重沟通与配合，确保任务高效完成。',
  },
  {
    content:
      '本人主动性强，充满干劲，勇于接受挑战。工作认真负责，追求进步，执行力强。具备强烈的学习欲望和能力，快速掌握所需技能，在团队中积极互动，推动共同进步',
  },
  {
    content:
      '我善于沟通，乐于助人，营造积极氛围。工作认真负责，有担当，执行力好。学习能力良好，能快速融入团队并发挥协调作用，促进团队高效合作与凝聚力',
  },
  {
    content:
      '本人学习能力突出，适应性强，对新领域充满探索欲。工作态度认真负责，执行力强，能快速转换角色。具备良好的沟通能力和团队合作精神，渴望在新环境中贡献价值并共同成长',
  },
])
const qzList = ref([])
const activeIndexTwo = ref(0)

// 计算日期范围 - 确保用户成年
const minDate = ref(null)
const maxDate = ref(null)
const defaultBirthday = ref(null)

// 切换身份
const changeIdentFun = () => {
  changeIdent()
}

onLoad(async () => {
  statusBarHeight.value = getStatusBar()
  // 使用纯时间戳进行计算
  const currentTimestamp = Date.now()
  const yearInMs = 365 * 24 * 60 * 60 * 1000 // 一年的毫秒数

  // 最大日期：16年前（确保成年）- 时间戳计算
  maxDate.value = currentTimestamp - 16 * yearInMs

  // 最小日期：60年前 - 时间戳计算
  minDate.value = currentTimestamp - 60 * yearInMs

  // 默认日期：16年前（最大值）- 时间戳计算
  defaultBirthday.value = currentTimestamp - 16 * yearInMs
  const res: any = await getDictData(DICT_IDS.JOB_SEEKSTATE)
  const expressiondata = res || res.data
  qzList.value = Object.entries(expressiondata).map(([key, value]) => ({
    value: key,
    label: value,
  }))
  formData.myLights = handHighlight[randomInt(0, handHighlight.length - 1)].content
})
onShow(() => {
  getInfoList()
})
const getInfoList = async () => {
  await uni.$onLaunched
  const res: any = await resumeBaseInfoList({
    id: getCheackInfo().baseInfoId,
  })
  if (res.code === 0) {
    if (Object.prototype.hasOwnProperty.call(res, 'data')) {
      formData.sex = res.data.sex
      formData.trueName = res.data.trueName
      activeIndex.value = res.data.sex === 1 ? 0 : 1
      // 赋值时强制转为时间戳
      formData.birthday = new Date(res.data.birthday).getTime()
      formData.seekStatus = res.data.seekStatus
      formData.myLights = res.data.myLights
    }
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
const submit = async () => {
  if (formData.trueName === '') {
    uni.showToast({
      title: '请填写真实姓名',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (!formData.birthday) {
    uni.showToast({
      title: '请选择出生年月',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  if (formData.myLights === '') {
    uni.showToast({
      title: '请输入个人亮点',
      icon: 'none',
      duration: 3000,
    })
    return
  }
  const res: any = await userResumeBaseInfo({
    ...formData,
    birthday: formatTime(formData.birthday).slice(0, 7),
  })

  if (res.code === 0) {
    uni.navigateTo({
      url: '/loginSetting/category/JobIntention',
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
const handlePickerChange = (e: any) => {
  formData.birthday = e.value
}
const changeactive = (index: any, item: any) => {
  activeIndex.value = index
  formData.sex = item.value
}
</script>

<style lang="scss" scoped>
::v-deep .u-input__content__field-wrapper__field {
  height: 70rpx !important;
}

::v-deep .u-line-progress__line {
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%) !important;
}
::v-deep .custom-class {
  background: #f7f7f7;
  border-radius: 20rpx !important;
}
::v-deep .custom-textarea-container-class {
  height: 200rpx;
  background: #f7f7f7;
}
::v-deep .wd-textarea__inner {
  height: 200rpx;
}
::v-deep .wd-picker__cell {
  width: 100% !important;
  padding: 10rpx 20rpx;
  border: 1rpx solid #e8e8e8 !important;
  border-radius: 8rpx;
}

.wd-picker {
  width: 100% !important;
}
.tag-select-r-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
}
.input-border {
  padding: 10rpx 20rpx !important;
  border: 1rpx solid #e8e8e8;
}
.month-picker {
  padding: 0rpx 0rpx;
}

.page_flex_left::after {
  position: absolute;
  bottom: 5rpx;
  left: 0rpx;
  width: 200rpx;
  height: 8rpx;
  content: '';
  background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
  border-radius: 32rpx;
}
.myStyle-box-pink {
  background: rgba(253, 239, 239, 1);
  border: 1rpx solid rgba(255, 190, 190, 1);
}
.btn_fixed {
  box-sizing: border-box;
  width: 100%;
  // padding: 0rpx 50rpx;
  margin-bottom: 100rpx;
  .btn_box {
    width: 100%;
    padding: 0rpx 50rpx 0rpx;
    padding-bottom: 0;

    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 30rpx;
      font-size: 16px;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}

.page_flex_for {
  display: flex;
  flex-direction: row;

  .page_flex_list {
    display: flex;
    flex-direction: row;
    margin-left: 20rpx;

    .page_select_border {
      padding: 0 22rpx;
      font-size: 22rpx;
      line-height: 44rpx;
      color: #3e9cff;
      background: #f1f1ff;
      border: 1px solid #3e9cff;
      border-radius: 5px 5px 5px 5px;
    }

    .page_border {
      padding: 0 22rpx;
      font-size: 22rpx;

      line-height: 44rpx;
      color: #777777;
      background: #f5f5f5;
      border-radius: 5px 5px 5px 5px;
    }
  }
}

.input_font {
  font-size: 22rpx;
  font-weight: 500;
  line-height: 44rpx;
  color: #777777;
}

.page_box {
  box-sizing: border-box;
  width: 100%;
  padding: 50rpx;
  padding-top: 100rpx;
}

.page_flex_left_just {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.page_flex_row_bottom {
  width: 100%;
  padding-top: 20rpx;
  padding-bottom: 10rpx;
}

.tag-name {
  padding-bottom: 10rpx;
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
}

.tag-select-r-list {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tag-select-r {
  width: 220rpx;
  padding: 10rpx 0rpx;
  margin: 20rpx 0rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #000;
  text-align: center;

  border-radius: 10rpx;
}
.tag-select-r-x {
  width: 270rpx;
  padding: 15rpx 0rpx;
  margin: 20rpx 0rpx;
  font-weight: 500;
  color: #000;
  text-align: center;
  background-color: #f2f2f2;
  border: 1rpx solid transparent;
  border-radius: 10rpx;
}

.tag-select-r-normal {
  background-color: #f2f2f2;
}
.myStyle-box {
  color: #3e9cff;
  background-color: #f1f1ff;
  border: 1rpx solid #3e9cff;
}

.page_padding {
  padding: 100rpx 50rpx 50rpx;
  background: #ffffff;
  border-radius: 20px 20px 20px 20px;
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
}

.page_input {
  padding-top: 16rpx;
  font-size: 26rpx;
  color: #666;
}

.page_input1 {
  padding-top: 10rpx;
}

.page_flex_left_row {
  display: flex;
  flex-direction: row;
  align-items: center;

  .page_flex_left_color {
    font-size: 11px;
    font-weight: 500;
    line-height: 22px;
    color: #333333;
  }

  .page_flex_des {
    font-size: 22rpx;
    font-weight: 500;
    line-height: 44rpx;
    color: #888888;
  }
}

.page_flex {
  display: flex;
  flex-direction: row;
  justify-content: space-between;

  .page_flex_left {
    position: relative;
    padding-top: 20rpx;
    padding-bottom: 10rpx;
    font-size: 46rpx;
    font-weight: 600;
    color: #000000;
  }

  .page_flex_icon {
    position: absolute;
    top: 0rpx;
    right: 60rpx;

    .page_flex_img {
      z-index: 1001;
      width: 300rpx;
      height: 300rpx;
      background-repeat: no-repeat;
      background-position: 100% 100%;
      background-size: 100% 100%;
      @include graph-img('/static/img/Mask_group');
    }
  }

  // .page_flex_right{
  // 	font-weight: 600;
  // 	font-size: 18px;
  // 	color: #000000;
  // 	line-height: 22px;
  // }
}
/* 兼容APP端picker-view居中 */
:deep(.uni-picker-view) {
  width: 100% !important;
  text-align: center !important;
}
:deep(.uni-picker-view-column) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  text-align: center !important;
}
:deep(.picker-item) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  text-align: center !important;
}
</style>
