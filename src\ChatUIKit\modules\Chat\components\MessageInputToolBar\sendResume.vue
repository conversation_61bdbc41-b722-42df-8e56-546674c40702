<template>
  <view class="flex justify-center" @tap="handleSendResume">
    <ItemContainer title="发送简历" :icon-url="resumeImg" />
  </view>
</template>

<script lang="ts" setup>
import { autorun } from 'mobx'
import type { InputToolbarEvent } from '@/ChatUIKit/types/index'
import ItemContainer from './itemContainer.vue'
import resumeImg from '@/ChatUIKit/static/message-custom/resume.png'

const toolbarInject = inject<InputToolbarEvent>('InputToolbarEvent')
const { sendCustomResumeMessage, getIMUserInfo, customCardInfo, getCoversationInfo } =
  useIMConversation()
const extUserInfo = ref<Api.IM.UserBusinessExtInfo>({})

async function handleSendResume() {
  const { conversationId } = getCoversationInfo.value
  if (!conversationId) {
    uni.showToast({
      title: '请稍后重试',
      icon: 'none',
    })
    return
  }
  try {
    await sendCustomResumeMessage(conversationId, {
      id: customCardInfo.value.positionInfoId,
      hxUserInfoVO: {
        userId: extUserInfo.value.hrUserId,
      },
    })
    toolbarInject?.closeToolbar()
  } catch {
    uni.showToast({
      title: '请完善简历后再投递',
      icon: 'none',
    })
    // message
    //   .confirm({
    //     title: '提示',
    //     msg: '请完善简历后再投递',
    //   })
    //   .then(() => {
    //     uni.navigateTo({
    //       url: '/resumeRelated/AttachmentResume/index',
    //     })
    //   })
    //   .catch()
  }
}

const unwatchUserInfo = autorun(() => {
  const { conversationId, conversationType } = getCoversationInfo.value
  if (conversationType === 'singleChat') {
    const { ext } = getIMUserInfo(conversationId)
    extUserInfo.value = JSON.parse(ext || '{}') as Api.IM.UserBusinessExtInfo
  }
})
onUnmounted(() => {
  unwatchUserInfo()
})
</script>

<style lang="scss" scoped>
//
</style>
