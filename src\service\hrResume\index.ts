import { POSTPaging } from '../index'
import type { hrIndexResumeUserListInt } from '@/service/hrIndex/types'
import { hrResumeResumeUserSeniorListDataInt } from './types'
import { HttpRequestConfig } from 'luch-request'

/** 职位下拉选数据查询接口 */
export const hrResumeResumeUserSeniorList = (
  data: Api.Request.IResPagingDataParamsInt<hrResumeResumeUserSeniorListDataInt>,
  config?: HttpRequestConfig,
) =>
  POSTPaging<hrIndexResumeUserListInt>(
    '/easyzhipin-api/hrResume/resumeUserSeniorList',
    data,
    config,
  )
