<template>
  <z-paging :fixed="false" layout-only safe-area-inset-bottom>
    <wd-config-provider :themeVars="themeVars">
      <view class="px-32rpx pb-40rpx flex flex-col gap-56rpx">
        <view class="flex flex-col gap-44rpx">
          <view
            class="rounded-38rpx h-76rpx w-344rpx bg-#2F2F2F px-36rpx flex items-center"
            @click="handleSelectPost"
          >
            <text class="c-#E4CC9C text-28rpx flex-1 line-clamp-1">
              {{ releaseSeniorPostActivePost.positionName }}
            </text>
            <text
              class="text-16rpx c-#E4CC9C"
              :class="modelShow ? 'i-carbon-triangle-solid' : 'i-carbon-triangle-down-solid'"
            />
          </view>
          <!-- <view class="flex items-center flex-wrap gap-x-34rpx gap-y-24rpx">
            <view
              v-for="(item, key) in releaseActivePost.positionKeyList"
              :key="`keywords-${key}`"
              class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
            >
              <text class="text-24rpx c-#D3D3D3">{{ item }}</text>
            </view>
          </view> -->
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">期望薪资</text>
            <view class="h-132rpx rounded-38rpx bg-#2F2F2F flex items-center">
              <wd-input
                v-model.number="seniorPostModel.salaryExpectationStart"
                no-border
                type="number"
                custom-class="flex-1"
                custom-input-class="text-center"
                placeholder="开始薪资"
                :maxlength="6"
              />
              <text class="text-36rpx font-500 c-#FFCB62">-</text>
              <wd-input
                v-model.number="seniorPostModel.salaryExpectationEnd"
                no-border
                type="number"
                custom-class="flex-1"
                custom-input-class="text-center"
                placeholder="结束薪资"
                :maxlength="6"
              />
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">求职状态</text>
            <view class="flex items-center flex-wrap gap-x-34rpx gap-y-24rpx">
              <view
                v-for="(item, key) in seekStatusOptions"
                :key="`seek-${key}`"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
                :class="{ 'border-#FFCB62': seniorPostModel.seekStatus === item.value }"
                @tap="handleSelectSeekStatus(item)"
              >
                <text
                  class="text-24rpx c-#D3D3D3"
                  :class="{ 'c-#FFCB62': seniorPostModel.seekStatus === item.value }"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">年龄要求</text>
            <view class="h-132rpx rounded-38rpx bg-#2F2F2F flex items-center">
              <wd-input
                v-model.number="seniorPostModel.ageBegin"
                no-border
                type="number"
                custom-class="flex-1"
                custom-input-class="text-center"
                placeholder="起始年龄"
                :maxlength="2"
              />
              <text class="text-36rpx font-500 c-#FFCB62">-</text>
              <wd-input
                v-model.number="seniorPostModel.ageEnd"
                no-border
                type="number"
                custom-class="flex-1"
                custom-input-class="text-center"
                placeholder="截止年龄"
                :maxlength="2"
              />
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">性别</text>
            <view class="grid grid-cols-3 gap-34rpx">
              <view
                v-for="(item, key) in genderOptions"
                :key="`gender-${key}`"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
                :class="{ 'border-#FFCB62': seniorPostModel.gender === item.value }"
                @tap="handleSelectGender(item)"
              >
                <text
                  class="text-24rpx c-#D3D3D3"
                  :class="{ 'c-#FFCB62': seniorPostModel.gender === item.value }"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
          <view class="flex flex-col gap-44rpx">
            <text class="c-#E4CC9C text-36rpx font-500">学历要求</text>
            <view class="flex items-center flex-wrap gap-x-34rpx gap-y-24rpx">
              <view
                v-for="(item, key) in qualificationOptions"
                :key="`qualification-${key}`"
                class="h-76rpx rounded-60rpx border-1px border-solid border-#9F9F9F center px-30rpx"
                :class="{ 'border-#FFCB62': seniorPostModel.qualification === item.value }"
                @tap="handleSelectQualification(item)"
              >
                <text
                  class="text-24rpx c-#D3D3D3"
                  :class="{ 'c-#FFCB62': seniorPostModel.qualification === item.value }"
                >
                  {{ item.text }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </wd-config-provider>
    <template #bottom>
      <view class="p-[36rpx_30rpx]">
        <wd-button
          custom-class="!bg-#2F2F2F !h-112rpx w-full"
          :round="false"
          @click="handleConfirm"
        >
          <text class="c-#E4CC9C text-28rpx">保存并筛选</text>
        </wd-button>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { DICT_IDS, Gender } from '@/enum'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { DictOption } from '@/hooks/common/useDictionary'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import type { hrResumeSeniorPostModelInt } from '@/service/hrResume/types'

const $emit = defineEmits<{
  (e: 'search', model: hrResumeSeniorPostModelInt): void
  (e: 'selectPost'): void
}>()
const modelShow = defineModel('show', {
  type: Boolean,
  default: false,
})
const { getDictOptions } = useDictionary()
const { releaseSeniorPostActivePost } = useReleasePost()
const themeVars: ConfigProviderThemeVars = {
  inputPlaceholderColor: '#FFCB62',
  inputColor: '#FFCB62',
  inputBg: 'transparent',
}
const seniorPostModel = ref<hrResumeSeniorPostModelInt>({})
const qualificationOptions = ref<DictOption[]>([])
const seekStatusOptions = ref<DictOption[]>([])
const genderOptions = ref<DictOption[]>([
  { value: Gender.UNSET, text: '不限' },
  { value: Gender.MALE, text: '男' },
  { value: Gender.FEMALE, text: '女' },
])
const fetchWorkEducationalOptions = async () => {
  const dictData = await getDictOptions(DICT_IDS.EDUCATION_REQUIREMENT)
  qualificationOptions.value = dictData
}
const fetchWorkStatusOptions = async () => {
  const dictData = await getDictOptions(DICT_IDS.SEEK_STATUS)
  seekStatusOptions.value = dictData
}
function handleSelectQualification(item: DictOption) {
  seniorPostModel.value.qualification =
    seniorPostModel.value.qualification === item.value ? undefined : (item.value as number)
}
function handleSelectGender(item: DictOption) {
  seniorPostModel.value.gender =
    seniorPostModel.value.gender === item.value ? undefined : (item.value as number)
}
function handleSelectSeekStatus(item: DictOption) {
  seniorPostModel.value.seekStatus =
    seniorPostModel.value.seekStatus === item.value ? undefined : (item.value as number)
}
function handleSelectPost() {
  $emit('selectPost')
}
function handleConfirm() {
  const filteredModel = Object.fromEntries(
    Object.entries(seniorPostModel.value).filter(
      ([_, value]) =>
        value !== undefined && value !== null && !(typeof value === 'string' && value === ''),
    ),
  )
  const isHaveModel = Object.keys(filteredModel).length
  if (!isHaveModel) {
    uni.showToast({
      title: '请先选择筛选条件',
      icon: 'none',
    })
    return
  }
  if (seniorPostModel.value.salaryExpectationStart && seniorPostModel.value.salaryExpectationEnd) {
    const startSalary = Number(seniorPostModel.value.salaryExpectationStart)
    const endSalary = Number(seniorPostModel.value.salaryExpectationEnd)
    if (startSalary >= endSalary) {
      uni.showToast({
        title: '开始薪资必须小于结束薪资',
        icon: 'none',
      })
      return
    }
  }
  if (seniorPostModel.value.ageBegin && seniorPostModel.value.ageEnd) {
    const startAge = Number(seniorPostModel.value.ageBegin)
    const endAge = Number(seniorPostModel.value.ageEnd)
    if (startAge >= endAge) {
      uni.showToast({
        title: '起始年龄必须小于截止年龄',
        icon: 'none',
      })
      return
    }
  }
  $emit('search', filteredModel)
}
onMounted(async () => {
  await uni.$onLaunched
  fetchWorkEducationalOptions()
  fetchWorkStatusOptions()
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  .wd-input__placeholder {
    text-align: center;
  }
}
:deep(.zp-view-super) {
  margin: 0 !important;
}
:deep(.zp-paging-container-content) {
  height: auto !important;
}
</style>
