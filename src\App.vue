<script setup lang="ts">
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useBaseUrlList } from '@/hooks/common/useBaseUrlList'

const { baseUrlMsgListIntApi } = useBaseUrlList()
const { initEaseMobIM } = useEaseMobIM()

onLaunch(async () => {
  console.log('App Launch')
  baseUrlMsgListIntApi()
  initEaseMobIM()
  // #ifdef APP-PLUS
  setTimeout(() => {
    plus.navigator.closeSplashscreen()
  }, 3000)
  // #endif
})
onShow(() => {
  console.log('App Show')
})
onHide(() => {
  console.log('App Hide')
})
</script>

<style lang="scss">
/* 每个页面公共css */
@import 'style/reset';
@import 'style/common';
@import 'style/iconfont-weapp-icon.css';
// @import 'components/gaoyia-parse/parse.css';
</style>
