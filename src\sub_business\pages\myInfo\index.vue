<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="我的"></CustomNavBar>
    </template>
    <view class="p-60rpx">
      <view class="flex items-center justify-center flex-col">
        <wd-upload
          reupload
          :before-upload="beforeUpload"
          v-model:file-list="fileList"
          image-mode="aspectFill"
          :limit="1"
          :header="header"
          :action="baseUrl"
          @success="successFun"
          accept="image"
          custom-class="custom-class-1"
        ></wd-upload>
        <view class="c-#666 text-24rpx m-l-[-20rpx] m-t-[-10rpx]" v-if="myInfo.headImgStatus === 0">
          {{ myInfo.headImgStatus === 0 ? '审核中' : '' }}
        </view>
      </view>

      <view class="m-b-40rpx">
        <view class="text-28rpx c-#000">账号</view>
        <view
          class="text-28rpx flex-1 py-20rpx c-#555 border-b-1rpx border-b-solid border-b-#E6E6E6"
        >
          {{ myInfo.phone }}
        </view>
      </view>
      <view class="m-b-40rpx">
        <view class="text-28rpx c-#000">姓名</view>
        <view
          class="text-28rpx flex-1 py-20rpx c-#555 border-b-1rpx border-b-solid border-b-#E6E6E6"
        >
          {{ myInfo.trueName }}
        </view>
      </view>
      <view class="m-b-40rpx">
        <view class="text-28rpx c-#000">当前公司</view>
        <view
          class="text-28rpx flex-1 py-20rpx c-#555 border-b-1rpx border-b-solid border-b-#E6E6E6"
        >
          {{ myInfo.companyName }}
        </view>
      </view>
      <view class="m-b-40rpx">
        <view class="text-28rpx c-#000">当前职位</view>
        <view
          @click="gohrPosition"
          class="flex-1 py-20rpx border-b-1rpx border-b-solid border-b-#E6E6E6 flex justify-between items-center"
        >
          <view class="text-28rpx" :class="myInfo.hrPosition ? 'c-#555' : 'c-#888'">
            {{ myInfo.hrPosition ? myInfo.hrPosition : '请输入当前职位' }}
          </view>
          <wd-icon name="chevron-right" color="#888888" size="18px"></wd-icon>
        </view>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { userImageAudit } from '@/interPost/my'
import { hruserInfo } from '@/service/hrUser'
import { quickHandlers } from '@/utils/compressImage'

// 定义用户信息类型
interface UserInfo {
  gender?: number
  headImgStatus?: number
  phone?: string
  trueName?: string
  companyName?: string
  hrPosition?: string
  headImgUrl?: string
}

const { getToken } = useUserInfo()
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const fileList = ref([])
const myInfo = ref<UserInfo>({})
// 部门
const hrPosition = ref('')
// 图片地址/attachment/uploadImgThum
const baseUrl = import.meta.env.VITE_SERVER_BASEURL + '/easyzhipin-api/attachment/uploadImgThum'
// 图片请求头
const header = computed(() => {
  return {
    token: getToken(),
  }
})

// before-upload 处理函数 - 使用预设的头像压缩配置
const beforeUpload = quickHandlers.avatar()

// 图片上传成功
const successFun = ({ fileList }) => {
  console.log('上传成功，文件列表:', fileList)
  const res = JSON.parse(fileList[0].response)
  if (res.code === 0) {
    getimgInfo(res.data[0].fileId)
  }
}

// 上传头像
const getimgInfo = async (fileId: any) => {
  const res: any = await userImageAudit({ fileId })
  if (res.code === 0) {
    myList()
  }
}

// hr基本信息列表
const myList = async () => {
  const res: any = await hruserInfo()
  if (res.code === 0) {
    if (res.data?.headImgUrl) {
      fileList.value = [
        {
          url: res.data.headImgUrl,
          name: 'tupian',
        },
      ]
    } else {
      fileList.value = [
        {
          url:
            res.data.gender === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png',
          name: 'tupian',
        },
      ]
    }
    myInfo.value = res.data
  }
}

// 部门
const gohrPosition = () => {
  const hrPosition = myInfo.value.hrPosition ? myInfo.value.hrPosition : ''
  uni.navigateTo({
    url: '/sub_business/pages/myInfo/model/hrPosition?hrPosition=' + hrPosition,
  })
}

uni.$on('refresh-myInfo', () => {
  myList()
})

onLoad(() => {
  myList()
})
</script>

<style scoped lang="scss">
:v-deep(.wd-input) {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
:v-deep(.wd-input__placeholder) {
  font-size: 32rpx !important;
}
:deep(.wd-input__inner) {
  font-size: 32rpx !important;
  font-weight: 500;
}
:deep(.wd-upload__picture) {
  border-radius: 50% !important;
}
:deep(.wd-upload__close) {
  display: none !important;
}
</style>
