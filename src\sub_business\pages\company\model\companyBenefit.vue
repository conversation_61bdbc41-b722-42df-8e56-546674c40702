<route lang="json5">
{ layout: 'default', style: { navigationBarTitleText: '', navigationStyle: 'custom' } }
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="福利待遇">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="mt-60rpx px-66rpx flex flex-col gap-106rpx">
      <view class="flex flex-col gap-38rpx">
        <view class="flex items-center gap-26rpx">
          <wd-input
            type="text"
            placeholder="自定义标签"
            v-model="labelCustomVal"
            no-border
            @confirm="handleAddCustomLabel"
          />
          <view
            class="center size-90rpx bg-[#5378FF] shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx"
            @click="handleAddCustomLabel"
          >
            <wd-icon name="add" size="26rpx" color="#ffffff" />
          </view>
        </view>
        <view class="flex items-center flex-wrap gap-30rpx" v-if="labelActiveList.length">
          <view
            class="center gap-10rpx h-56rpx px-10rpx rounded-10rpx border-1px border-dashed border-[#5378FF]"
            v-for="(item, key) in labelActiveList"
            :key="`label-active-${key}`"
          >
            <text class="c-#5378FF text-24rpx font-400">{{ item.welfareTreatment }}</text>
            <wd-icon
              name="close-circle"
              size="24rpx"
              color="#5378FF"
              @click="handleDeleteLabel(key)"
            />
          </view>
        </view>
      </view>
      <view class="flex flex-col gap-38rpx" v-if="labelList.length">
        <text class="c-#333333 font-bold text-36rpx">可选标签</text>
        <view class="flex items-center flex-wrap gap-30rpx">
          <view
            class="center h-56rpx px-10rpx rounded-10rpx border-1px border-dashed border-[#888888]"
            v-for="(item, key) in labelList"
            :key="`label-${key}`"
            @click="handleSelectLabel(item)"
          >
            <text class="c-#888888 text-24rpx font-400">{{ item.welfareTreatment }}</text>
          </view>
        </view>
      </view>
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="handleConfirm">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { updateWelfare } from '@/service/hrCompany'

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const salaryArray = [
  { welfareTreatment: '不加班' },
  { welfareTreatment: '氛围好' },
  { welfareTreatment: '年终奖' },
  { welfareTreatment: '全勤奖' },
  { welfareTreatment: '下午茶' },
  { welfareTreatment: '五险一金' },
  { welfareTreatment: '朝九晚五' },
  { welfareTreatment: '周末双休' },
  { welfareTreatment: '学历不限' },
  { welfareTreatment: '经验不限' },
  { welfareTreatment: '带薪培训' },
  { welfareTreatment: '定期团建' },
  { welfareTreatment: '带薪年假' },
  { welfareTreatment: '节日福利' },
  { welfareTreatment: '生日福利' },
  { welfareTreatment: '保底工资' },
  { welfareTreatment: '近地铁口' },
  { welfareTreatment: '正常工作制' },
  { welfareTreatment: '无销售性质' },
  { welfareTreatment: '接受无经验' },
  { welfareTreatment: '办公室坐班' },
  { welfareTreatment: '不接受居家办公' },
]
// 返回函数
const back = () => {
  uni.navigateBack()
}

// 定义福利待遇类型
interface WelfareItem {
  welfareTreatment: string
}

const labelCustomVal = ref('')
const labelActiveList = ref<WelfareItem[]>([])
const labelList = ref<WelfareItem[]>([])

function handleSelectLabel(label: WelfareItem) {
  const exists = labelActiveList.value.some(
    (item) => item.welfareTreatment === label.welfareTreatment,
  )
  if (!exists) {
    labelActiveList.value.push(label)
  }
}

function handleDeleteLabel(key: number) {
  labelActiveList.value.splice(key, 1)
}

function handleAddCustomLabel() {
  if (labelCustomVal.value) {
    const exists = labelActiveList.value.some(
      (item) => item.welfareTreatment === labelCustomVal.value,
    )
    if (!exists) {
      labelActiveList.value.push({ welfareTreatment: labelCustomVal.value })
      labelCustomVal.value = ''
    }
  }
}
// 初始化标签列表
const updateLabelList = () => {
  // 设置可选标签为福利待遇标签
  labelList.value = salaryArray
}

// 确认选择
const handleConfirm = async () => {
  try {
    // 调用更新公司福利待遇接口
    const res: any = await updateWelfare({
      welfareList: labelActiveList.value.map((item) => item.welfareTreatment),
    })

    if (res.code === 0) {
      uni.navigateBack({
        delta: 1,
        success() {
          uni.$emit('welfare-updated')
        },
      })
    } else {
      uni.showToast({
        title: res.msg || '更新失败',
        icon: 'none',
        duration: 3000,
      })
    }
  } catch (error) {
    uni.showToast({
      title: '更新失败',
      icon: 'none',
      duration: 3000,
    })
  }
}

// 页面加载时处理参数和初始化
onLoad((options) => {
  // 初始化可选标签
  updateLabelList()

  // 处理传递过来的福利待遇参数
  if (options.companyWelfareList) {
    try {
      const companyWelfareList = JSON.parse(decodeURIComponent(options.companyWelfareList))
      if (Array.isArray(companyWelfareList)) {
        // 将字符串数组转换为对象数组
        labelActiveList.value = companyWelfareList.map((item) => {
          if (typeof item === 'string') {
            return { welfareTreatment: item }
          } else if (item && typeof item === 'object' && item.welfareTreatment) {
            return item
          } else {
            return { welfareTreatment: String(item) }
          }
        })
      }
    } catch (error) {
      console.error('解析福利待遇参数失败:', error)
      labelActiveList.value = []
    }
  }
})

defineExpose({
  submitData: handleConfirm,
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  flex: 1;
  padding: 22rpx 22rpx;
  border-radius: 20rpx;
  box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);
}
.btn-fixed {
  padding: 40rpx 80rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 25rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
