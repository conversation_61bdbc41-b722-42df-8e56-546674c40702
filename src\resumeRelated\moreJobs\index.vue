<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging ref="pagingRef" v-model="pageData" @query="queryList" :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="更多岗位">
        <template #left>
          <wd-icon
            name="arrow-left"
            class="back-button"
            color="#000000"
            size="20"
            @click="goBack"
          />
        </template>
      </CustomNavBar>
      <view class="page-top">
        <view class="content_flex">
          <view class="content_search">
            <view class="content_search_bg">
              <view class="content_search_left">
                <image
                  class="_image"
                  src="/static/images/home/<USER>"
                  mode="aspectFill"
                ></image>
              </view>
              <view class="content_search_right">
                <wd-input
                  type="text"
                  no-border
                  placeholder="搜索您想要的内容"
                  v-model="params.entity.positionName"
                  confirm-type="search"
                  clearable
                  @clear="handleClear"
                  @confirm="confirm"
                ></wd-input>
              </view>
            </view>
          </view>
        </view>
      </view>
    </template>
    <JobCardList
      :job-list="pageData || []"
      @go-detail="goDetail"
      @go-job="goJob"
      @go-chat="goChat"
    />
    <!-- v-if="
              !loadingList[index] && hasLoadedList[index] && (pageDataList[index] || []).length > 0
            "
            :job-list="pageDataList[index] || []"
            :index="index"
            @go-detail="goDetail"
            @go-job="goJob"
            @go-chat="goChat"
            :isShow="true" -->
  </z-paging>
  <wd-toast />
  <wd-message-box />
</template>

<script setup lang="ts">
import { useMessage } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { queryPositionByCompanyId } from '@/interPost/home'
import { numberTokw } from '@/utils/common'
import { useLoginStore } from '@/store'
import { ChatUIKit } from '@/ChatUIKit/index'
import { truncateText } from '@/utils/util'
import jz from '@/static/img/home/<USER>'
import pk from '@/static/img/home/<USER>'
import resumeMatching from '@/static/common/resume-matching.png'
import JobCardList from '@/components/home/<USER>/index.vue'
const appUserStore = ChatUIKit.appUserStore
defineOptions({
  name: 'HomePersonal',
})
// vuex数据
const { setmyjobList } = usePosition()
const loginStore = useLoginStore()
const message = useMessage()
const { getDictLabel } = useDictionary()
const { userRoleIsRealName } = useUserInfo()
const { sendGreetingMessage, sendResumeMessage } = useIMConversation()
const { bool: sentResumesBool } = useBoolean()
const { pagingRef, pageInfo, pageData, pageStyle, pageSetInfo } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})

const title = ref('')
const companyId = ref('')
const params = reactive({
  orderBy: {},
  entity: {
    positionName: '',
    id: '',
  },
})

const userToRealName = () => {
  if (!userRoleIsRealName.value) {
    message
      .confirm({
        title: '提示',
        msg: '请先实名认证',
      })
      .then(() => {
        uni.navigateTo({
          url: '/setting/identityAuth/index',
        })
      })
      .catch()
    return Promise.reject(new Error('请先实名认证'))
  }
  return Promise.resolve(1)
}

// 去沟通item
const goChat = async (item: AnyObject) => {
  try {
    await userToRealName()
    item.companyId = companyId.value
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    if (hxUserInfoVO?.username) {
      sendGreetingMessage(hxUserInfoVO.username, item)
    }
  } catch (error) {}
}
const goDetail = (id: any) => {
  uni.navigateTo({
    url: `/resumeRelated/jobDetail/index?id=${id}&companyId=${companyId.value}`,
  })
}

// 输入框清空事件
const handleClear = () => {
  pagingRef.value.reload()
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 搜索
const confirm = () => {
  pagingRef.value.reload()
}
const goJob = async (item: AnyObject) => {
  try {
    await userToRealName()
    const hxUserInfoVO = item?.hxUserInfoVO || {}
    item.companyId = companyId.value
    if (hxUserInfoVO?.username) {
      const num = await sendResumeMessage(hxUserInfoVO.username, item)
      if (!num) {
        message
          .confirm({
            title: '提示',
            msg: '请完善简历后再投递',
          })
          .then(() => {
            uni.navigateTo({
              url: '/resumeRelated/AttachmentResume/index',
            })
          })
          .catch()
      }
    }
  } catch (error) {}
}
// 获取列表positionKey
const queryList = async (page, size) => {
  console.log('获取列表position')
  pageSetInfo(page, size)
  const res: any = await queryPositionByCompanyId({
    ...params,
    page: pageInfo.page,
    size: pageInfo.size,
  })
  if (res.code === 0) {
    // 匹配度和竞争力
    setmyjobList(res.data.list)
    // 渲染数据操作
    res.data.list.forEach(async (ele: any) => {
      loginStore.myjobFillterList.forEach((item) => {
        if (item.id === ele.id) {
          ele.competitiveness = item.competitiveness
          ele.matchingDegree = item.matchingDegree
          ele.count = item.count
        }
      })
      // 头像处理
      if (!ele.hrPositionUrl) {
        ele.hrPositionUrl =
          ele.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
      }
      // 渲染数据操作
      ele.sizeName = await getDictLabel(100, ele.sizeName)
      // 安全处理 positionKey，确保它是一个数组
      if (ele.positionKey && typeof ele.positionKey === 'string') {
        ele.positionKey = ele.positionKey.split(',').filter((item) => item && item.trim())
      } else if (!Array.isArray(ele.positionKey)) {
        ele.positionKey = []
      }
      ele.workSalaryBegin =
        ele.workSalaryBegin === 0 ? '面议' : numberTokw(ele.workSalaryBegin + '')
      ele.workSalaryEnd = ele.workSalaryEnd === 0 ? '' : numberTokw(ele.workSalaryEnd + '')
      // 距离处理
      if (ele.distanceMeters) {
        const distance = Math.floor(parseInt(ele.distanceMeters) / 1000)
        if (distance === 0) {
          ele.distanceMeters = '<1km'
        } else {
          ele.distanceMeters = distance + 'km'
        }
      }
      // 在线状态
      const eleData = await appUserStore.getUserInfoFromStore(ele.hxUserInfoVO.username)
      ele.hxUserInfoVO.isOnline = !!eleData.isOnline
    })

    pagingRef.value.complete(res.data.list)
  }
}

onLoad(async (options) => {
  await uni.$onLaunched
  companyId.value = options?.companyId
  params.entity.id = options?.companyId
  pagingRef.value.reload()
})
</script>

<style lang="scss" scoped>
.back-button {
  position: absolute;
  left: 10px;
}

.title {
  font-size: 50rpx;
  font-weight: 500;
  color: #000000;
}

.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 0rpx 40rpx 0rpx;
  padding-bottom: 0;
  margin-bottom: 30rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 20rpx 30rpx;
      background: #ffffff;
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        align-items: center;
        width: 10%;
        ._image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}
</style>
