<template>
  <z-paging
    ref="pagingRef"
    v-model="pageData"
    auto
    :fixed="false"
    :paging-style="pageStyle"
    :refresher-enabled="false"
    :show-loading-more-no-more-view="false"
    @query="queryList"
  >
    <view class="flex flex-col pt-30rpx">
      <view
        v-for="(item, key) in pageData"
        :key="key"
        class="px-52rpx py-40rpx flex flex-col gap-22rpx border-b-1 border-b-solid border-b-#D9D9D9"
        @tap="handleSelectPost(item)"
      >
        <view class="flex items-center">
          <text class="c-#000000 text-30rpx flex-1 line-clamp-1">
            {{ item.positionName }}
          </text>
        </view>
        <text class="c-#555555 text-24rpx">
          {{ formatAddress(item) }}
        </text>
      </view>
    </view>
  </z-paging>
</template>

<script lang="ts" setup>
import { EMIT_EVENT } from '@/enum'
import { hrPositionQueryOptionList } from '@/service/hrPosition'
import { useReleasePost } from '@/hooks/business/useReleasePost'
import type { hrPositionQueryOptionListInt } from '@/service/hrPosition/types'

const $emits = defineEmits<{
  (e: 'selectPost'): void
}>()
const { releaseActivePost } = useReleasePost()
const { pagingRef, pageInfo, pageSetInfo, pageStyle, pageData } =
  usePaging<hrPositionQueryOptionListInt>({
    style: {},
  })

function formatAddress(item: hrPositionQueryOptionListInt) {
  const { provinceName, cityName, districtName } = item
  const isDirectMunicipality = provinceName === cityName
  const addressParts = isDirectMunicipality
    ? [provinceName, districtName]
    : [provinceName, cityName, districtName]
  return addressParts.filter(Boolean).join('')
}
async function fetchPagingHrPositionQueryOptionList() {
  await uni.$onLaunched
  const { data } = await hrPositionQueryOptionList({
    ...pageInfo,
  })
  const { list, total } = data
  pagingRef.value.completeByTotal(list, total)
}

function queryList(page: number, size: number) {
  pageSetInfo(page, size)
  fetchPagingHrPositionQueryOptionList()
}
function handleSelectPost(item: hrPositionQueryOptionListInt) {
  releaseActivePost.value = item
  $emits('selectPost')
}
function reload() {
  pagingRef.value?.reload()
}
uni.$on(EMIT_EVENT.REFRESH_PUBLISH_POSITION, reload)

onBeforeUnmount(() => {
  uni.$off(EMIT_EVENT.REFRESH_PUBLISH_POSITION)
})
</script>

<style lang="scss" scoped>
:deep(.zp-view-super) {
  margin: 0 !important;
}
:deep(.zp-paging-container-content) {
  height: auto !important;
}
</style>
