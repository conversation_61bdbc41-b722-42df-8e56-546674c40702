<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>消息传递测试</title>
    <style>
      body {
        padding: 20px;
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
      }
      .container {
        max-width: 600px;
        padding: 20px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      .test-btn {
        display: block;
        width: 100%;
        padding: 15px;
        margin: 10px 0;
        font-size: 16px;
        color: white;
        cursor: pointer;
        background-color: #007aff;
        border: none;
        border-radius: 8px;
      }
      .test-btn:hover {
        background-color: #0056b3;
      }
      .debug-info {
        max-height: 300px;
        padding: 15px;
        margin-top: 20px;
        overflow-y: auto;
        font-family: monospace;
        font-size: 12px;
        background-color: #f8f9fa;
        border-radius: 5px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>消息传递测试页面</h2>

      <button class="test-btn" onclick="testDirectCall()">测试直接调用父页面方法</button>
      <button class="test-btn" onclick="testUniMessage()">测试uni.postMessage</button>
      <button class="test-btn" onclick="testParentMessage()">测试window.parent.postMessage</button>
      <button class="test-btn" onclick="testUploadSuccess()">测试上传成功消息</button>

      <div class="debug-info" id="debugInfo">
        <div>调试信息将显示在这里...</div>
      </div>
    </div>

    <script>
      const debugInfo = document.getElementById('debugInfo')

      function addDebugInfo(message) {
        const timestamp = new Date().toLocaleTimeString()
        const debugLine = document.createElement('div')
        debugLine.innerHTML = `[${timestamp}] ${message}`
        debugInfo.appendChild(debugLine)
        debugInfo.scrollTop = debugInfo.scrollHeight
        console.log(message)
      }

      // 页面加载完成
      document.addEventListener('DOMContentLoaded', function () {
        addDebugInfo('页面加载完成')
        addDebugInfo(`window.parent存在: ${!!window.parent}`)
        addDebugInfo(`window.uni存在: ${!!window.uni}`)
        addDebugInfo(
          `window.parent.handleWebViewMessage存在: ${!!(window.parent && window.parent.handleWebViewMessage)}`,
        )

        // 延迟检查父页面方法
        setTimeout(() => {
          addDebugInfo('延迟检查父页面方法...')
          addDebugInfo(
            `window.parent.handleWebViewMessage存在: ${!!(window.parent && window.parent.handleWebViewMessage)}`,
          )
          if (window.parent && window.parent.handleWebViewMessage) {
            addDebugInfo('✓ 父页面方法可用')
          } else {
            addDebugInfo('✗ 父页面方法仍不可用')
          }
        }, 1000)

        // 再次延迟检查
        setTimeout(() => {
          addDebugInfo('再次检查父页面方法...')
          addDebugInfo(
            `window.parent.handleWebViewMessage存在: ${!!(window.parent && window.parent.handleWebViewMessage)}`,
          )
          if (window.parent && window.parent.handleWebViewMessage) {
            addDebugInfo('✓ 父页面方法可用')
          } else {
            addDebugInfo('✗ 父页面方法仍不可用')
          }
        }, 2000)
      })

      // 测试直接调用父页面方法
      function testDirectCall() {
        addDebugInfo('=== 测试直接调用父页面方法 ===')

        const testMessage = {
          type: 'UPLOAD_SUCCESS',
          data: {
            fileId: 'direct-call-test',
            fileName: 'test.pdf',
            fileSize: 1024,
          },
        }

        try {
          if (window.parent && window.parent.handleWebViewMessage) {
            window.parent.handleWebViewMessage({ detail: testMessage })
            addDebugInfo('✓ 直接调用父页面方法成功')
          } else {
            addDebugInfo('✗ 父页面方法不可用，尝试延迟调用')
            // 延迟重试
            setTimeout(() => {
              if (window.parent && window.parent.handleWebViewMessage) {
                window.parent.handleWebViewMessage({ detail: testMessage })
                addDebugInfo('✓ 延迟调用父页面方法成功')
              } else {
                addDebugInfo('✗ 延迟调用也失败')
              }
            }, 500)
          }
        } catch (error) {
          addDebugInfo(`✗ 直接调用失败: ${error.message}`)
        }
      }

      // 测试uni.postMessage
      function testUniMessage() {
        addDebugInfo('=== 测试uni.postMessage ===')

        const testMessage = {
          type: 'UPLOAD_SUCCESS',
          data: {
            fileId: 'uni-message-test',
            fileName: 'test.pdf',
            fileSize: 1024,
          },
        }

        try {
          if (window.uni && window.uni.postMessage) {
            window.uni.postMessage({
              data: testMessage,
            })
            addDebugInfo('✓ uni.postMessage发送成功')
          } else {
            addDebugInfo('✗ uni.postMessage不可用')
          }
        } catch (error) {
          addDebugInfo(`✗ uni.postMessage失败: ${error.message}`)
        }
      }

      // 测试window.parent.postMessage
      function testParentMessage() {
        addDebugInfo('=== 测试window.parent.postMessage ===')

        const testMessage = {
          type: 'UPLOAD_SUCCESS',
          data: {
            fileId: 'parent-message-test',
            fileName: 'test.pdf',
            fileSize: 1024,
          },
        }

        try {
          if (window.parent) {
            window.parent.postMessage(testMessage, '*')
            addDebugInfo('✓ window.parent.postMessage发送成功')

            // 延迟再次发送，确保消息被接收
            setTimeout(() => {
              window.parent.postMessage(testMessage, '*')
              addDebugInfo('延迟重发window.parent.postMessage')
            }, 1000)
          } else {
            addDebugInfo('✗ window.parent不可用')
          }
        } catch (error) {
          addDebugInfo(`✗ window.parent.postMessage失败: ${error.message}`)
        }
      }

      // 测试上传成功消息
      function testUploadSuccess() {
        addDebugInfo('=== 测试上传成功消息 ===')

        const testMessage = {
          type: 'UPLOAD_SUCCESS',
          data: {
            code: 0,
            msg: 'success',
            data: [
              {
                fileId: 205,
                url: 'https://www.easyzhipin.com/easyzhipin-api/attachment/imageView/9bbb5b05dca91b15c0d147ab1716f773',
              },
            ],
          },
        }

        // 尝试所有方式
        try {
          if (window.parent && window.parent.handleWebViewMessage) {
            window.parent.handleWebViewMessage({ detail: testMessage })
            addDebugInfo('✓ 直接调用父页面方法成功')
          }
        } catch (error) {
          addDebugInfo(`✗ 直接调用失败: ${error.message}`)
        }

        try {
          if (window.uni && window.uni.postMessage) {
            window.uni.postMessage({
              data: testMessage,
            })
            addDebugInfo('✓ uni.postMessage发送成功')
          }
        } catch (error) {
          addDebugInfo(`✗ uni.postMessage失败: ${error.message}`)
        }

        try {
          if (window.parent) {
            window.parent.postMessage(testMessage, '*')
            addDebugInfo('✓ window.parent.postMessage发送成功')
          }
        } catch (error) {
          addDebugInfo(`✗ window.parent.postMessage失败: ${error.message}`)
        }
      }

      // 监听消息
      window.addEventListener('message', function (event) {
        addDebugInfo(`收到window消息: ${JSON.stringify(event.data)}`)
      })

      // 兼容uni-app的消息接收方式
      if (window.uni && window.uni.onMessage) {
        window.uni.onMessage(function (event) {
          addDebugInfo(`收到uni消息: ${JSON.stringify(event.data)}`)
        })
      }
    </script>
  </body>
</html>
