<template>
  <view class="flex flex-col gap-10rpx bg-#ffffff">
    <view class="flex items-stretch h-64rpx">
      <view class="center flex-1 gap-6rpx" @tap="handleOperatePhrases('edit')">
        <wd-icon :name="phrasesEdit" size="36rpx" />
        <text class="c-#333333 text-24rpx">修改</text>
      </view>
      <view class="w-1px bg-#EAEAEA" />
      <view class="center flex-1 gap-6rpx" @tap="handleOperatePhrases('add')">
        <wd-icon :name="phrasesAdd" size="36rpx" />
        <text class="c-#333333 text-24rpx">添加</text>
      </view>
    </view>
    <view class="flex items-center flex-row flex-wrap h-340rpx overflow-y-scroll px-50rpx">
      <view
        v-for="(item, key) in phrasesText"
        :key="key"
        class="py-30rpx border-b-1px border-b-solid border-b-[#E8E8E8] w-full flex items-center gap-28rpx"
      >
        <text class="c-#555555 text-24rpx line-clamp-1 flex-1" @click="selectPhrase(item)">
          {{ item }}
        </text>
        <wd-button plain custom-class="!min-w-112rpx !h-52rpx" @click="sendPhrase(item)">
          <text class="text-24rpx">发送</text>
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { USER_TYPE } from '@/enum'
import phrasesAdd from '@/ChatUIKit/static/message-tool-bar/phrases-add.png'
import phrasesEdit from '@/ChatUIKit/static/message-tool-bar/phrases-edit.png'

const emits = defineEmits(['sendPhrase', 'selectPhrase'])

const { userIntel } = useUserInfo()

const commonPhrases = {
  [USER_TYPE.APPLICANT]: [
    '您好，我觉得这个岗位非常适合我， 如果您也觉得我合适，期待您的回复。',
    '您好，我非常希望能够得到这个岗位的面试机会，如果岗位还有空缺，希望能得到机会，感谢。',
    '请问，这个岗位还有空缺?我觉得自己很适合，能够胜任，希望进一步的与您介绍自己。',
    '请问，贵公司的产品经理职位(示例职位)还在招人吗?',
    '我对这个职位很感兴趣，希望您能看看我的简历，谢谢!',
    '您好!看了您的招聘信息，非常感兴趣。',
    '刚刚看了您发布的这个职位，我特别喜欢，可否聊聊呢?',
    'Boss，您好!希望您能看看我的资料，期待能有深入沟通!',
    '对贵司很有感，如有需要可以随时联系我，感谢，打扰',
    '您好，Boss!期待深入沟通!',
    '个人觉得我和贵公司这一岗位很匹配，可以聊聊么?',
  ],
  [USER_TYPE.HR]: [
    '请问在考虑新的工作机会吗?',
    '您好，看了您的简历，不知道可否深入聊聊？',
    '想与您沟通下我们在招的这个职位，现在方便吗?',
    '您好，请问在么?方便沟通一下吗?',
    '您好，看了您的在线简历，与我们招聘岗位非常匹配，所以对您也比较感兴趣。请问您现在方便吗?我们可以简单聊下吗?',
    '您好，看了您的基础信息，很感兴趣，也很期待和您进一步沟通下这个岗位。您是否方便?',
  ],
}
const phrasesText = computed(() => commonPhrases[userIntel.value.type] || [])
const sendPhrase = CommonUtil.debounce((phrase: string) => {
  emits('sendPhrase', phrase)
}, 300)
const selectPhrase = (phrase: string) => {
  emits('selectPhrase', phrase)
}

function handleOperatePhrases(type: 'add' | 'edit') {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/sub_common/pages/phrases/index', {
      type,
    }),
  })
}
</script>

<style lang="scss" scoped></style>
