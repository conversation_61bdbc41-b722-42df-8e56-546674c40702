<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
    'app-plus': {
      softinputMode: 'adjustResize',
    },
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-textarea
        :auto-focus="true"
        clear-trigger="focus"
        :adjust-position="false"
        v-model="myLights"
        :maxlength="100"
        clearable
        show-word-limit
        placeholder="请输入常用语"
        custom-class="custom-class"
        custom-textarea-container-class="custom-textarea-container-class"
        custom-textarea-class="custom-textarea-class"
      />
    </view>
    <template #bottom>
      <view class="btn-fixed">
        <view class="btn_box" @click="addWork">
          <view class="btn_bg">完成</view>
        </view>
      </view>
    </template>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { updateMyLights } from '@/interPost/resume'
import { useMessage } from 'wot-design-uni'
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const myLights = ref('')
// 初始化
const initNane = ref('')
const message = useMessage()
// 是否是add或edit
const isAdd = ref(null)
// id
const baseInfoId = ref(null)
onLoad(async (options) => {
  await nextTick()
  myLights.value = options.myLights
  initNane.value = options.myLights
  baseInfoId.value = options.id
  isAdd.value = options.isAdd
  console.log(options)
})
// 完成
const addWork = async () => {
  const res: any = await updateMyLights({ baseInfoId: baseInfoId.value, myLights: myLights.value })
  if (res.code === 0) {
    uni.navigateBack()
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 返回
const back = () => {
  if (myLights.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
.corporateName {
  padding: 40rpx 40rpx;
}
::v-deep .wd-textarea__placeholder {
  font-size: 26rpx;
}
::v-deep .custom-class {
  border-radius: 20rpx !important;
  box-shadow: 8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1);
}
::v-deep .custom-textarea-container-class {
  height: calc(100vh - 1200rpx);
}
::v-deep .wd-textarea__inner {
  height: calc(100vh - 480rpx);
}
.btn-fixed {
  box-sizing: border-box;
  justify-content: center;
  padding: 40rpx 40rpx;
  .btn_box {
    box-sizing: border-box;
    width: 100%;
    .btn_bg {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0rpx;
      font-size: 28rpx;
      font-weight: 500;
      color: #333333;
      background: linear-gradient(90deg, #ffc2c2 0%, #dddcff 100%);
      border-radius: 14px 14px 14px 14px;
    }
  }
}
</style>
