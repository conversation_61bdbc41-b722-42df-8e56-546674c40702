<template>
  <view class="flex justify-center" @tap="handleSendPhone">
    <ItemContainer title="换电话" :icon-url="phoneImg" />
  </view>
</template>

<script lang="ts" setup>
import { autorun } from 'mobx'
import type { InputToolbarEvent } from '@/ChatUIKit/types/index'
import ItemContainer from './itemContainer.vue'
import phoneImg from '@/ChatUIKit/static/message-custom/phone.png'

const toolbarInject = inject<InputToolbarEvent>('InputToolbarEvent')
const { sendCustomPhoneMessage, getIMUserInfo, getCoversationInfo } = useIMConversation()
const extUserInfo = ref<Api.IM.UserBusinessExtInfo>({})

async function handleSendPhone() {
  const { conversationId } = getCoversationInfo.value
  if (!conversationId) {
    uni.showToast({
      title: '请稍后重试',
      icon: 'none',
    })
    return
  }
  sendCustomPhoneMessage(conversationId, extUserInfo.value.hrUserId)
  toolbarInject?.closeToolbar()
}

const unwatchUserInfo = autorun(() => {
  const { conversationId, conversationType } = getCoversationInfo.value
  if (conversationType === 'singleChat') {
    const { ext } = getIMUserInfo(conversationId)
    extUserInfo.value = JSON.parse(ext || '{}') as Api.IM.UserBusinessExtInfo
  }
})
onUnmounted(() => {
  unwatchUserInfo()
})
</script>

<style lang="scss" scoped>
//
</style>
