<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <CustomNavBar title="公司简称">
        <template #left>
          <wd-icon @click="back" name="arrow-left" class="back-button" color="#000" size="20" />
        </template>
        <template #right>
          <view @click="submit" class="text-28rpx c-#000">确认</view>
        </template>
      </CustomNavBar>
    </template>
    <view class="corporateName">
      <wd-input no-border v-model="shortName" placeholder="请输入公司简称" focus />
    </view>
    <wd-message-box />
  </z-paging>
</template>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { updateShortName } from '@/service/hrCompany'
// 公司

const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
// 公司简称
const shortName = ref('')
// 初始化
const initNane = ref('')
const message = useMessage()
onLoad(async (options) => {
  await nextTick()
  shortName.value = options.shortName
  initNane.value = options.shortName
})
const submit = async () => {
  const res = await updateShortName({ shortName: shortName.value })
  if (res.code === 0) {
    uni.navigateBack({
      delta: 1,
      success() {
        uni.$emit('welfare-updated')
      },
    })
  } else {
    uni.showToast({
      title: res.msg,
      icon: 'none',
      duration: 3000,
    })
  }
}
// 返回
const back = () => {
  if (shortName.value !== initNane.value) {
    message
      .confirm({
        title: '提示',
        msg: '您有内容未提交保存,确认返回吗?',
      })
      .then(() => {
        uni.navigateBack()
      })
  } else {
    uni.navigateBack()
  }
}
</script>

<style scoped lang="scss">
::v-deep .wd-input {
  width: 100%;
  text-align: left;
  background-color: transparent;
}
::v-deep .wd-input__placeholder {
  font-size: 32rpx !important;
}
::v-deep .wd-input__inner {
  font-size: 32rpx !important;
  font-weight: 500;
}
.corporateName {
  padding-bottom: 40rpx;
  margin: 40rpx 40rpx;
  border-bottom: 1rpx solid #c0bfbf;
}
</style>
