<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="bg-img">
    <CustomNavBar title="面试"></CustomNavBar>
    <view class="page-time flex items-center p-l-40rpx p-r-40rpx p-t-40rpx">
      <view class="text-100rpx font-600 c-#000">{{ currentDate.slice(5, 7) }}</view>
      <view>
        <view class="text-32rpx c-#555">月</view>
        <view class="text-32rpx c-#555">{{ currentDate.slice(0, 4) }}年</view>
      </view>
    </view>

    <z-paging
      ref="pagingRef"
      :fixed="false"
      v-model="pageData"
      @query="queryList"
      :paging-style="pageStyle"
      safe-area-inset-bottom
      :style="{ height: `calc(100vh - ${customBar * 2}rpx - 200rpx)` }"
    >
      <template #top>
        <view class="m-l-40rpx m-r-40rpx">
          <scroll-view
            ref="dateScrollRef"
            scroll-x
            class="white-space-nowrap w-[calc(100% - 80rpx)]"
            :scroll-left="scrollLeft"
          >
            <view class="gap-60rpx flex items-center">
              <view
                v-for="(item, index) in monthDays"
                :key="index"
                :id="'date-' + index"
                class="text-28rpx text-center"
                :class="selectedIndex === index ? 'date-selected' : ''"
                style="min-width: 80rpx; padding: 10rpx 0; cursor: pointer"
                @click="selectDate(index, item)"
              >
                <view>{{ item.week }}</view>
                <view>{{ item.date.slice(8, 10) }}</view>
              </view>
            </view>
          </scroll-view>
        </view>
      </template>

      <view class="pageList m-t-40rpx">
        <view class="c-#888 text-32rpx p-b-20rpx p-l-40rpx" v-if="pageData.length > 0">时间</view>
        <view
          class="pageList-item flex items-start"
          v-for="(item, index) in pageData"
          :key="index"
          @tap="handleToPreviewDetail(item)"
        >
          <view class="pageList-item-left">
            <view class="c-#000 text-34rpx text-right">{{ item.agreeTime.slice(11, 13) }}点</view>
          </view>
          <view class="pageList-item-right m-l-30rpx pageList-right p-l-40rpx p-b-40rpx">
            <view
              class="pageList-item-right-card relative"
              :class="item.status === 0 || item.status === 1 ? 'bg-#4399ff' : 'bg-#F2F2F2'"
            >
              <view class="flex items-center">
                <view class="w-90rpx">
                  <image
                    class="w-76rpx h-76rpx rounded-full"
                    :src="item.userUrl"
                    mode="aspectFill"
                  ></image>
                </view>
                <view class="flex-1">
                  <view class="flex items-center justify-between">
                    <view
                      class="text-30rpx p-b-5rpx u-line-1"
                      :class="item.status === 0 || item.status === 1 ? 'c-#fff' : 'c-#000'"
                    >
                      {{ item.userName }}
                    </view>
                  </view>

                  <view class="flex justify-between">
                    <view
                      class="text-28rpx"
                      :class="item.status === 0 || item.status === 1 ? 'c-#fff' : 'c-#000'"
                    >
                      {{ item.positionName }}
                    </view>
                    <view
                      class="text-24rpx"
                      :class="item.status === 0 || item.status === 1 ? 'c-#fff' : 'c-#000'"
                    >
                      {{ item.salaryStart }}{{ item.salaryEnd ? '-' + item.salaryEnd : '' }}
                    </view>
                  </view>
                </view>
              </view>
              <view
                class="absolute top-[0rpx] right-[-10rpx] z-100"
                v-if="item.status === 0 || item.status === 1"
              >
                <wd-img :width="35" :height="35" :src="interview" />
              </view>

              <view
                v-if="item.status === 2 || item.status === 3"
                class="c-#FF5B5B text-24rpx border-1rpx border-solid border-#FF5B5B rounded-[10rpx] px-10rpx absolute top-[30rpx] right-[20rpx] z-1000"
              >
                {{ item.status === 2 ? '已拒绝' : '已取消' }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </z-paging>
  </view>
</template>

<script setup lang="ts">
import { CommonUtil } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import { hrInterviewRecordList } from '@/service/hrBiographical'
import { getCustomBar } from '@/utils/storage'
import { getMonthDays, formatDateDay, numberTokw } from '@/utils/common'
import interview from '@/sub_business/static/interview/interview.png'

const { pagingRef, pageData, pageStyle, pageInfo, pageSetInfo } = usePaging({
  style: {
    padding: '60rpx 0rpx 0rpx',
    borderRadius: '50rpx 50rpx 0rpx 0rpx',
    boxShadow: '8rpx 8rpx 32rpx 0rpx rgba(0, 0, 0, 0.1)',
    background: '#ffffff',
    marginTop: '20rpx',
  },
})
// 当前的月份对应的日期和星期
const monthDays = ref([])
// 当前日期
const currentDate = ref('')
const customBar = ref(null)
// 新增：高亮选中索引
const selectedIndex = ref(0)
const scrollToId = computed(() => 'date-' + selectedIndex.value)
const dateScrollRef = ref()
const scrollLeft = ref(0)
const isInitialized = ref(false)

function scrollDateToCenter(idx) {
  // 在APP端需要更长的延迟确保DOM完全渲染
  const delay =
    uni.getSystemInfoSync().platform === 'android' || uni.getSystemInfoSync().platform === 'ios'
      ? 500
      : 200

  setTimeout(() => {
    nextTick(() => {
      if (!dateScrollRef.value || idx < 0 || idx >= monthDays.value.length) {
        console.log('scrollDateToCenter: 参数无效', { idx, length: monthDays.value.length })
        return
      }

      // 在APP端使用更可靠的查询方式
      const query = uni.createSelectorQuery()
      query.select(`#date-${idx}`).boundingClientRect()
      query.select('.white-space-nowrap').boundingClientRect()
      query.exec((res) => {
        console.log('scrollDateToCenter: 查询结果', res)

        const itemRect = res[0]
        const scrollRect = res[1]

        if (itemRect && scrollRect) {
          // 计算需要滚动的距离，让选中项居中
          const targetScrollLeft =
            itemRect.left - scrollRect.left - (scrollRect.width - itemRect.width) / 2

          console.log('scrollDateToCenter: 计算滚动距离', {
            itemRect,
            scrollRect,
            targetScrollLeft,
          })

          // 使用动画效果平滑滚动
          const currentScrollLeft = scrollLeft.value || 0
          const distance = Math.max(0, targetScrollLeft) - currentScrollLeft
          const duration = 300 // 300ms的动画时间
          const startTime = Date.now()

          const animateScroll = () => {
            const elapsed = Date.now() - startTime
            const progress = Math.min(elapsed / duration, 1)

            // 使用缓动函数使动画更自然
            const easeOutQuart = 1 - Math.pow(1 - progress, 4)

            scrollLeft.value = currentScrollLeft + distance * easeOutQuart

            if (progress < 1) {
              // 在APP端使用setTimeout替代requestAnimationFrame
              setTimeout(animateScroll, 16) // 约60fps
            }
          }

          animateScroll()
        } else {
          // 备用方案：使用简单的计算方式
          console.log('scrollDateToCenter: 使用备用方案')
          const itemWidth = 80 // 每个日期项的宽度
          const gap = 60 // 间距
          const totalItemWidth = itemWidth + gap
          const targetScrollLeft = idx * totalItemWidth - 100 // 减去一些偏移量
          scrollLeft.value = Math.max(0, targetScrollLeft)
        }
      })
    })
  }, delay) // 根据平台调整延迟时间
}
// 跳转详情页
const handleToPreviewDetail = (item) => {
  const hrDetailItem = JSON.stringify({
    userId: item.receiveUserId,
  })
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/resumeRelated/preview/index', {
      hrDetailItem,
    }),
  })
}
const selectDate = async (idx, item) => {
  selectedIndex.value = idx
  currentDate.value = item.date
  console.log(idx, item, 'idx==========')
  await pagingRef.value.reload()
  // scrollDateToCenter(idx)
}

// 监听monthDays，数据变化后自动高亮今日并居中
watch(
  monthDays,
  (val) => {
    if (val && val.length > 0) {
      const todayIdx = val.findIndex((item) => item.isToday)
      console.log('watch monthDays: 找到今日索引', { todayIdx, total: val.length })

      // 只在初始化时执行滚动，避免重复滚动
      if (!isInitialized.value) {
        isInitialized.value = true
        // 先设置选中状态，然后延迟滚动
        selectedIndex.value = todayIdx !== -1 ? todayIdx : 0
        console.log('初始化：设置选中索引', selectedIndex.value)

        // 根据平台调整延迟时间，确保DOM完全渲染
        const delay =
          uni.getSystemInfoSync().platform === 'android' ||
          uni.getSystemInfoSync().platform === 'ios'
            ? 600
            : 300
        setTimeout(() => {
          console.log('开始滚动到今日')
          scrollDateToCenter(selectedIndex.value)
        }, delay)
      } else {
        // 非初始化时，直接设置选中状态
        selectedIndex.value = todayIdx !== -1 ? todayIdx : 0
        console.log('非初始化：设置选中索引', selectedIndex.value)
      }
    }
  },
  { immediate: true },
)
// 历史免滤
const goHistory = () => {
  uni.navigateTo({
    url: '/resumeRelated/interview/WaitMeeting',
  })
}
const queryList = async (page, size) => {
  pageSetInfo(page, size)
  const res: any = await hrInterviewRecordList({
    entity: {
      createTime: currentDate.value,
    },
    orderBy: {},
    page: pageInfo.page,
    size: pageInfo.size,
  })
  console.log(res, 'res==========')
  if (res.code === 0) {
    res.data.list.forEach((item) => {
      item.salaryStart = item.salaryStart === 0 ? '面议' : numberTokw(item.salaryStart + '')
      item.salaryEnd = item.salaryEnd === 0 ? '' : numberTokw(item.salaryEnd + '')
      if (!item.userUrl) {
        item.userUrl =
          item.sex === 1 ? '/static/header/hrheader1.png' : '/static/header/hrheader2.png'
      }
    })
    pagingRef.value.complete(res.data.list)
  }
}
onLoad(async (options) => {
  await uni.$onLaunched
  customBar.value = getCustomBar()
  monthDays.value = getMonthDays()
  currentDate.value = formatDateDay()

  console.log('onLoad: 初始化数据', {
    monthDaysLength: monthDays.value.length,
    currentDate: currentDate.value,
    todayIndex: monthDays.value.findIndex((item) => item.isToday),
  })

  await pagingRef.value.reload()
  console.log(currentDate.value, 'currentDate==========')

  // 页面加载完成后，再次尝试滚动到今日（备用方案）
  // const delay = uni.getSystemInfoSync().platform === 'android' || uni.getSystemInfoSync().platform === 'ios' ? 800 : 500
  // setTimeout(() => {
  //   const todayIdx = monthDays.value.findIndex((item) => item.isToday)
  //   if (todayIdx !== -1 && selectedIndex.value === todayIdx) {
  //     console.log('onLoad: 备用滚动方案')
  //     scrollDateToCenter(todayIdx)
  //   }
  // }, delay)
})

// 页面显示时再次尝试滚动到今日
// onShow(() => {
//   setTimeout(() => {
//     const todayIdx = monthDays.value.findIndex((item) => item.isToday)
//     if (todayIdx !== -1) {
//       console.log('onShow: 强制滚动到今日', todayIdx)
//       selectedIndex.value = todayIdx
//       scrollDateToCenter(todayIdx)
//     }
//   }, 100)
// })
</script>

<style lang="scss" scoped>
.pageList {
  box-sizing: border-box;
  padding: 0rpx 40rpx;
}
.pageList-right {
  border-left: 1rpx solid #cccaca;
}
.pageList-item-right-card {
  width: 494rpx;
  padding: 30rpx;
  border-radius: 20rpx;
}
.date-selected {
  color: #fff !important;
  background: orange !important;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.text-28rpx {
  transition: all 0.3s ease;
}
</style>
