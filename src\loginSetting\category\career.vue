<route lang="json5">
{
  layout: 'default',
  style: {
    navigationBarTitleText: '',
    enablePullDownRefresh: false,
    navigationStyle: 'custom',
  },
}
</route>
<template>
  <view class="h-screen flex flex-col bg-img">
    <CustomNavBar title="职位"></CustomNavBar>

    <!-- 搜索区域 -->
    <view class="flex-shrink-0 border-b border-gray-200">
      <view class="content_flex">
        <view class="content_search">
          <view class="content_search_bg">
            <view class="content_search_left">
              <image src="/static/img/search.png" mode="aspectFill"></image>
            </view>
            <view class="content_search_right">
              <wd-input
                no-border
                placeholder="搜索您想要的内容"
                v-model="keyword"
                confirm-type="search"
                @confirm="confirmSearch"
              ></wd-input>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex flex-1 min-h-0">
      <!-- 左侧父级分类 -->
      <scroll-view scroll-y class="w-230rpx flex-shrink-0">
        <view v-for="(parentName, pIndex) in positionDataList" :key="pIndex">
          <view
            :class="[
              activeFIndex === pIndex ? 'activeBg' : 'normalBg',
              pIndex === activeFIndex - 1 ? 'prev-selected' : '',
              pIndex === activeFIndex + 1 ? 'next-selected' : '',
              'page-list-left-text',
            ]"
            class="page-list-left-text"
            @click="activeF(pIndex)"
          >
            {{ parentName.name }}
          </view>
        </view>
      </scroll-view>

      <!-- 右侧子级及孙子级内容 -->
      <scroll-view scroll-y class="flex-1 min-w-0">
        <view v-for="(child, cIndex) in currentChildren" :key="cIndex">
          <view class="page-list-right-p">
            <view class="page-list-right-title">{{ child.name }}</view>
            <view class="page-tag-list">
              <view
                v-for="(grandchild, gIndex) in child.subLevelModelList"
                :key="gIndex"
                class="tag-select-r"
                :class="grandchild.active ? 'myStyle-box' : ''"
                @click="selectPosition(cIndex, gIndex)"
              >
                {{ grandchild.name }}
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { CommonUtil } from 'wot-design-uni'
import CustomNavBar from '@/components/CustomNavBar/CustomNavBar.vue'
import positionData from '@/utils/json/position.json'
import { useLoginStore } from '@/store'

const loginStore = useLoginStore()
const keyword = ref('')
const activeFIndex = ref(0)
const positionDataList = ref([])
const originalPositionList = ref([])
const isSearching = ref(false)

const currentChildren = computed(() => {
  return positionDataList.value[activeFIndex.value]?.subLevelModelList || []
})

const positionSearch = (searchKeyword: string) => {
  if (!searchKeyword.trim()) return originalPositionList.value

  const keyword = searchKeyword.toLowerCase().trim()
  const results = []

  originalPositionList.value.forEach((category) => {
    const categoryMatches = category.name.toLowerCase().includes(keyword)
    const matchedSubcategories = []

    if (category.subLevelModelList) {
      category.subLevelModelList.forEach((subcategory) => {
        const subcategoryMatches = subcategory.name.toLowerCase().includes(keyword)
        const matchedPositions =
          subcategory.subLevelModelList?.filter((position) =>
            position.name.toLowerCase().includes(keyword),
          ) || []
        if (subcategoryMatches || matchedPositions.length > 0) {
          matchedSubcategories.push({
            ...subcategory,
            subLevelModelList: subcategoryMatches
              ? subcategory.subLevelModelList
              : matchedPositions,
          })
        }
      })
    }
    if (categoryMatches || matchedSubcategories.length > 0) {
      results.push({
        ...category,
        subLevelModelList: categoryMatches ? category.subLevelModelList : matchedSubcategories,
        _matchType: categoryMatches ? 'category' : 'subcategory',
      })
    }
  })
  return results.sort((a, b) => {
    if (a._matchType === 'category' && b._matchType !== 'category') return -1
    if (b._matchType === 'category' && a._matchType !== 'category') return 1
    return 0
  })
}

const confirmSearch = () => {
  const searchKeyword = keyword.value.trim()
  if (searchKeyword) {
    isSearching.value = true
    const searchResults = positionSearch(searchKeyword)
    positionDataList.value = searchResults
    if (searchResults.length > 0) {
      activeFIndex.value = 0
    }
  } else {
    clearSearch()
  }
}

const clearSearch = () => {
  isSearching.value = false
  positionDataList.value = originalPositionList.value
  keyword.value = ''
  initSelectedData()
}

// 监听搜索关键词变化
watch(keyword, (newKeyword) => {
  if (!newKeyword.trim() && isSearching.value) {
    clearSearch()
  }
})

const activeF = (index) => {
  activeFIndex.value = index
}

const resetAllActive = () => {
  for (const parent of positionDataList.value) {
    for (const child of parent.subLevelModelList || []) {
      for (const grandchild of child.subLevelModelList || []) {
        grandchild.active = false
      }
    }
  }
}

// 构建职位选择对象
const buildPositionSelection = (categoryIndex, subcategoryIndex, positionIndex) => {
  const category = positionDataList.value[categoryIndex]
  const subcategory = category.subLevelModelList[subcategoryIndex]
  const position = subcategory.subLevelModelList[positionIndex]
  const result = reactive({
    expectedPositions: position.name,
    expectedPositionsCode: position.code,
    expectedPositionsSearchKey: position.searchKey,
    fourthLevelPositions: [],
  })
  if (position.subLevelModelList && position.subLevelModelList.length > 0) {
    result.fourthLevelPositions = position.subLevelModelList
  }
  return result
}

// 完成职位选择
const completePositionSelection = (positionObj) => {
  console.log(positionObj, 'positionObj=====')
  loginStore.setpositionData(positionObj)
  uni.navigateBack()
}

// 选择职位
const selectPosition = (subcategoryIndex, positionIndex) => {
  resetAllActive()

  const targetPosition =
    positionDataList.value[activeFIndex.value].subLevelModelList[subcategoryIndex]
      .subLevelModelList[positionIndex]

  targetPosition.active = true

  const positionSelection = buildPositionSelection(
    activeFIndex.value,
    subcategoryIndex,
    positionIndex,
  )
  completePositionSelection(positionSelection)
}

const initializePositionData = () => {
  const rawData = CommonUtil.deepClone(positionData.zpData.position)
  const usedCodes = new Set()
  return rawData.map((category) => ({
    ...category,
    subLevelModelList: category.subLevelModelList?.map((subcategory) => ({
      ...subcategory,
      subLevelModelList: subcategory.subLevelModelList?.filter((position) => {
        if (usedCodes.has(position.code)) return false
        usedCodes.add(position.code)
        return true
      }),
    })),
  }))
}
const initSelectedData = () => {
  const selectedCode = (loginStore.positionObj as AnyObject)?.expectedPositionsCode
  if (!selectedCode) return
  for (let pIndex = 0; pIndex < positionDataList.value.length; pIndex++) {
    const category = positionDataList.value[pIndex]
    for (const subcategory of category.subLevelModelList || []) {
      const position = subcategory.subLevelModelList?.find((p) => p.code === selectedCode)
      if (position) {
        position.active = true
        activeFIndex.value = pIndex
        return
      }
    }
  }
}

onLoad(() => {
  const initializedData = initializePositionData()
  originalPositionList.value = initializedData
  positionDataList.value = initializedData
  initSelectedData()
})
</script>

<style lang="scss" scoped>
:deep(.wd-input) {
  width: 100%;
  background-color: transparent !important;
  .wd-input__placeholder {
    font-size: 28rpx !important;
    color: #fff !important;
  }
  .wd-input__inner {
    font-size: 28rpx !important;
    font-weight: 500;
    color: #fff !important;
  }
}
.content_flex {
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 40rpx 40rpx;
  padding-bottom: 20rpx;

  .content_search {
    display: flex;
    flex-direction: row;
    width: 100%;

    .content_search_bg {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 100%;
      padding: 15rpx 30rpx;
      color: #fff;
      background: rgba(61, 61, 61, 0.34);
      border-radius: 80rpx;
      box-shadow: 8rpx 8rpx 33rpx 0rpx rgba(0, 0, 0, 0.1);

      .content_search_left {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 10%;

        image {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .content_search_right {
        width: 90%;
        padding-left: 10rpx;
      }
    }
  }
}

.containner-select-list {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx 0 !important;
}

.myStyle-box::after {
  position: absolute;
  right: 0rpx;
  bottom: 0rpx;
  width: 32rpx;
  height: 28rpx;
  content: '';
  background-image: url('../img/Mask_group(2).png');
}

.page-list-right-title {
  padding-bottom: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.page-list-right-p {
  padding: 30rpx 20rpx 0rpx !important;
  margin-bottom: 30rpx;
}

.activeBg {
  font-weight: 500;
  color: #1160ff;
  background: transparent;
}

.tag-select {
  position: relative;
  width: 200rpx;
  padding: 8rpx 0;
  color: #555;
  text-align: center;
  background-color: #fff;
  border: 1px solid #1160ff;
  border-radius: 10rpx;
}

.normalBg {
  background: #e8e8e8;
  // border: 1px solid transparent;
}

.u-searchs {
  padding: 0 46rpx;
  border-bottom: 1rpx solid $uni-border-b-color;
}

.page-list {
  display: flex;
  padding: 0;

  &-left {
    width: 230rpx;
    // padding: 20rpx 0 0;

    &-text {
      padding: 30rpx 10rpx 30rpx 10rpx;
      font-size: 28rpx;
      text-align: center;
      transition: all 0.3s;

      &.prev-selected {
        border-bottom-right-radius: 20rpx;
      }

      &.next-selected {
        border-top-right-radius: 20rpx;
      }
    }
  }

  &-right {
    flex: 1;

    &-p {
      padding: 40rpx 30rpx;

      &-title {
        margin-bottom: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
  }
}

.page-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  align-items: center;
  justify-content: space-between;

  .tag-select-r {
    position: relative;
    width: 225rpx;
    padding: 12rpx 4rpx !important;
    margin-bottom: 10rpx;
    font-size: 26rpx;
    color: #333;
    text-align: center;
    background: #f5f5f5;
    border-radius: 10rpx;
    transition: all 0.3s;

    &.myStyle-box {
      border: 1px solid #1160ff;
    }
  }
}
</style>
