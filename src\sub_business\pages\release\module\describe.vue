<template>
  <view class="px-50rpx mt-30rpx flex flex-col gap-30rpx">
    <view
      class="h-844rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx overflow-hidden p-20rpx flex flex-col gap-20rpx"
    >
      <wd-textarea
        v-model="releasePostModel.positionDesc"
        placeholder="请输入岗位职责的相关内容"
        :maxlength="1000"
        custom-class="flex-1"
      />
      <view class="flex justify-end">
        <view class="center gap-12rpx bg-#E0ECFF rounded-30rpx h-60rpx w-180rpx">
          <wd-img :src="deepseekIcon" width="92rpx" height="16rpx" />
        </view>
      </view>
    </view>
    <view
      class="h-168rpx bg-white shadow-[8rpx_8rpx_33rpx_0rpx_rgba(0,0,0,0.1)] rounded-20rpx flex items-center gap-20rpx px-36rpx"
      @tap="handleSelectWorkerAddress"
    >
      <view class="flex flex-col flex-1 gap-16rpx">
        <view class="flex items-center gap-10rpx">
          <text class="c-#333333 text-24rpx">工作地址</text>
          <view class="flex items-center gap-4rpx" v-if="!releaseActiveAddress?.status">
            <wd-icon name="info-circle" size="34rpx" color="#FF0000" />
            <text class="c-#FF0000 text-26rpx">未认证</text>
          </view>
        </view>
        <text class="line-clamp-1 c-#333333 text-26rpx">
          {{ releaseActiveAddress?.address ?? '请选择工作地址' }}
        </text>
      </view>
      <wd-icon name="arrow-right" size="30rpx" color="#8A8A8A" />
    </view>
    <view class="flex items-start gap-8rpx">
      <view>
        <wd-icon name="error-circle" size="24rpx" color="#888888" />
      </view>
      <view>
        <text class="c-#888888 text-22rpx font-400">
          发布职位即表示同意
          <text class="c-#4128FF">《 职位发布规范 》</text>
          ，如违反规则将可能导致您的账号被封禁
        </text>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import { useReleasePost } from '@/sub_business/hooks/useReleasePost'
import { hrCompanyWorkAddressQueryPassList } from '@/service/hrCompanyWorkAddress'
import deepseekIcon from '@/sub_business/static/release/deepseek.png'

const { releasePostModel, releaseActiveAddress } = useReleasePost()
async function fetchHrCompanyWorkAddressQueryPassList() {
  const { data } = await hrCompanyWorkAddressQueryPassList({
    entity: {},
    page: 1,
    size: 1,
  })
  const [first] = data.list
  if (first?.id) {
    releaseActiveAddress.value = first
    releasePostModel.value.companyWorkAddressId = first.id
  }
}

function handleSelectWorkerAddress() {
  uni.navigateTo({
    url: CommonUtil.buildUrlWithParams('/sub_business/pages/AddressCenter/index', {
      source: 'release',
    }),
  })
}

onMounted(() => {
  fetchHrCompanyWorkAddressQueryPassList()
})
</script>

<style lang="scss" scoped>
:deep(.wd-textarea) {
  .wd-textarea__value {
    height: 100%;
  }
  .wd-textarea__inner {
    height: 100%;
  }
}
</style>
