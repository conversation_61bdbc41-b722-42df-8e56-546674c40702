<template>
  <z-paging layout-only :paging-style="pageStyle">
    <template #top>
      <wd-config-provider :themeVars="themeVars">
        <wd-navbar
          :bordered="false"
          safe-area-inset-top
          placeholder
          custom-class="!bg-transparent px-30rpx"
        >
          <template #left>
            <wd-icon :name="thunderPromptImg" size="54rpx" />
          </template>
          <template #right>
            <view class="flex items-center gap-30rpx">
              <wd-icon :name="messagePromptImg" size="54rpx" />
            </view>
          </template>
        </wd-navbar>
        <view class="px-40rpx mt-28rpx">
          <wd-search
            placeholder-left
            hide-cancel
            custom-class="!p-0 !bg-transparent"
            placeholder="搜索联系人、聊天记录、备注"
          />
          <view class="border-t-1px border-t-solid border-t-[#DEDDDD] mx--40rpx px-40rpx my-34rpx">
            <view class="w-580rpx">
              <wd-tabs v-model="newsTabsStatus" line-width="80rpx" line-height="10rpx">
                <wd-tab
                  v-for="item in newsTabsList"
                  :key="item.name"
                  :title="`${item.label}`"
                  :name="item.name"
                />
              </wd-tabs>
            </view>
          </view>
        </view>
      </wd-config-provider>
    </template>
    <view class="px-40rpx">
      <newsBusinessList @chat="handleChat" />
    </view>
    <template #bottom>
      <customTabbar name="news" />
    </template>
  </z-paging>
</template>

<script lang="ts" setup>
import { CommonUtil } from 'wot-design-uni'
import type { ConfigProviderThemeVars } from 'wot-design-uni'
import customTabbar from '@/components/common/custom-tabbar.vue'
import newsBusinessList from '@/components/news/news-business-list.vue'
import messagePromptImg from '@/static/common/message-prompt.png'
import thunderPromptImg from '@/static/common/thunder-prompt.png'

defineOptions({
  name: 'NewsBusiness',
})
const { pageStyle } = usePaging({
  style: {
    background: 'linear-gradient( 125deg, #FFDEDE 0%, #EBEFFA 20%, #FFFFFF 100%)',
  },
})
const themeVars: ConfigProviderThemeVars = {
  searchInputBg: '#FFFFFF',
  searchInputHeight: '82rpx',
  searchInputRadius: '80rpx',
  searchPlaceholderColor: '#333333',
  searchIconSize: '36rpx',
  searchIconColor: '#333333',
  searchInputFs: '24rpx',
  searchInputColor: ' #333333',
  searchCancelColor: '#333333',
  tabsNavLineBgColor: '#FFA7A7',
  tabsNavFs: '26rpx',
}
const newsTabsStatus = ref(0)
const newsTabsList = [
  {
    label: '全部',
    name: 0,
  },
  {
    label: '新招呼',
    name: 1,
  },
  {
    label: '仅沟通',
    name: 2,
  },
  {
    label: '已交换',
    name: 3,
  },
  {
    label: '已约面',
    name: 4,
  },
]
const handleChat = async (id: string) => {
  try {
    uni.navigateTo({
      url: CommonUtil.buildUrlWithParams('/ChatUIKit/modules/Chat/index', {
        type: 'singleChat',
        id,
      }),
    })
  } catch (error) {}
}
</script>

<style lang="scss" scoped>
:deep(.wd-tabs) {
  background: transparent;
  .wd-tabs__line {
    bottom: 6px;
  }
  .wd-tabs__nav {
    background: transparent;
  }
  .wd-tabs__nav-item {
    &.is-active {
      font-size: 30rpx;
    }
  }
}
</style>
