import type { Message } from 'wot-design-uni/components/wd-message-box/types'
import type { Toast } from 'wot-design-uni/components/wd-toast/types'

interface MessageOptions {
  chatWotMessage: (selector?: string) => Message
  chatWotToast: (selector?: string) => Toast
}

class ChatMessageManager {
  private static instance: ChatMessageManager
  private _chatWotMessage?: (selector?: string) => Message
  private _chatWotToast?: (selector?: string) => Toast

  private constructor() {}

  static getInstance(): ChatMessageManager {
    if (!ChatMessageManager.instance) {
      ChatMessageManager.instance = new ChatMessageManager()
    }
    return ChatMessageManager.instance
  }

  init(options: MessageOptions) {
    this._chatWotMessage = options.chatWotMessage
    this._chatWotToast = options.chatWotToast
  }

  get chatWotMessage() {
    if (!this._chatWotMessage) {
      throw new Error('ChatMessage not initialized. Please call useChatMessage() first.')
    }
    return this._chatWotMessage
  }

  get chatWotToast() {
    if (!this._chatWotToast) {
      throw new Error('ChatToast not initialized. Please call useChatMessage() first.')
    }
    return this._chatWotToast
  }
}

const manager = ChatMessageManager.getInstance()

export const useChatMessage = (options: MessageOptions) => {
  manager.init(options)
  return {
    chatWotMessage: manager.chatWotMessage,
    chatWotToast: manager.chatWotToast,
  }
}

// 导出全局访问方法
export const getChatMessage = () => manager.chatWotMessage
export const getChatToast = () => manager.chatWotToast
