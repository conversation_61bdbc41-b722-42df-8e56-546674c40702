{"name": "easyzhipin", "type": "commonjs", "version": "2.6.3", "description": "易直聘", "license": "MIT", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"preinstall": "npx only-allow pnpm", "uvm": "npx @dcloudio/uvm@latest", "uvm-rm": "node ./scripts/postupgrade.js", "postuvm": "echo upgrade uni-app success!", "dev:h5": "uni", "dev-test:h5": "uni --mode test", "dev:mp": "uni -p mp-weixin", "build:dev-mp-weixin": "uni build -p mp-weixin --mode development", "build:prd-mp-weixin": "uni build -p mp-weixin", "prepare": "git init && husky install", "type-check": "vue-tsc --noEmit", "cz": "czg"}, "lint-staged": {"src/!(ChatUIKit|uni_modules)/**/*.{html,vue,ts,cjs,json,md}": ["prettier --write"], "src/!(ChatUIKit|uni_modules)/**/*.{vue,js,ts,jsx,tsx}": ["eslint --cache --fix"], "src/!(ChatUIKit|uni_modules)/**/*.{vue,css,scss,html}": ["stylelint --fix"]}, "resolutions": {"bin-wrapper": "npm:bin-wrapper-china"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4050720250324001", "@dcloudio/uni-app-harmony": "3.0.0-4050720250324001", "@dcloudio/uni-app-plus": "3.0.0-4050720250324001", "@dcloudio/uni-components": "3.0.0-4050720250324001", "@dcloudio/uni-h5": "3.0.0-4050720250324001", "@dcloudio/uni-mp-alipay": "3.0.0-4050720250324001", "@dcloudio/uni-mp-baidu": "3.0.0-4050720250324001", "@dcloudio/uni-mp-harmony": "3.0.0-4050720250324001", "@dcloudio/uni-mp-jd": "3.0.0-4050720250324001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4050720250324001", "@dcloudio/uni-mp-lark": "3.0.0-4050720250324001", "@dcloudio/uni-mp-qq": "3.0.0-4050720250324001", "@dcloudio/uni-mp-toutiao": "3.0.0-4050720250324001", "@dcloudio/uni-mp-weixin": "3.0.0-4050720250324001", "@dcloudio/uni-mp-xhs": "3.0.0-4050720250324001", "@dcloudio/uni-quickapp-webview": "3.0.0-4050720250324001", "abortcontroller-polyfill": "^1.7.8", "dayjs": "1.11.10", "easemob-websdk": "4.15.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "luch-request": "^3.1.1", "mobx": "6.13.4", "p-limit": "^6.2.0", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "pinyin-pro": "3.26.0", "qs": "6.5.3", "sm-crypto": "^0.3.13", "vue": "3.4.21", "vue-i18n": "^9.1.9", "wot-design-uni": "^1.8.0", "z-paging": "^2.8.7"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@dcloudio/types": "^3.4.14", "@dcloudio/uni-automator": "3.0.0-4050720250324001", "@dcloudio/uni-cli-shared": "3.0.0-4050720250324001", "@dcloudio/uni-stacktracey": "3.0.0-4050720250324001", "@dcloudio/vite-plugin-uni": "3.0.0-4050720250324001", "@esbuild/darwin-arm64": "0.20.2", "@esbuild/darwin-x64": "0.20.2", "@iconify-json/carbon": "^1.2.4", "@rollup/rollup-darwin-x64": "^4.28.0", "@types/node": "^20.17.9", "@types/sm-crypto": "^0.3.4", "@types/wechat-miniprogram": "^3.4.8", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@uni-helper/uni-types": "1.0.0-alpha.6", "@uni-helper/vite-plugin-uni-layouts": "^0.1.10", "@uni-helper/vite-plugin-uni-manifest": "^0.2.8", "@uni-helper/vite-plugin-uni-pages": "0.2.28", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@unocss/preset-legacy-compat": "^0.59.4", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.20", "commitlint": "^18.6.1", "czg": "^1.9.4", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.32.0", "husky": "^8.0.3", "lint-staged": "^15.2.10", "postcss": "^8.4.49", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.12.0", "sass": "1.77.8", "stylelint": "^16.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^14.0.1", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-prettier": "^5.0.2", "terser": "^5.36.0", "typescript": "^5.7.2", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^0.17.8", "vite": "5.2.8", "vite-plugin-restart": "^0.4.2", "vue-tsc": "^1.8.27"}}