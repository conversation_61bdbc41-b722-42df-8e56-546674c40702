import { ref } from 'vue'
import { defineStore } from 'pinia'
import { USER_TYPE } from '@/enum'
import { currentValueOf, timeDiffValueOf, valueOfToDate } from '@/utils/days'

type UserType = Partial<Api.User.IUserInfo>
const initState: UserType = {
  token: '',
  type: USER_TYPE.APPLICANT,
}
export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<UserType>({ ...initState })
    const isLoginEd = computed(() => !!userInfo.value.token)
    const setUserLoginTime = () => {
      userInfo.value.loginTime = currentValueOf()
    }
    const setUserInfo = (val: UserType) => {
      const { token } = val
      userInfo.value = {
        ...userInfo.value,
        ...val,
      }
      // userInfo.value.type = 1 // 本地测用
      setUserToken(token)
    }
    const setUserRoleType = (type: Api.Common.USER_TYPE) => {
      userInfo.value.type = type
    }
    const setUserToken = (token?: string) => {
      userInfo.value.token = token
      setUserLoginTime()
    }
    const getUserLoginTimeDiff = () => {
      return timeDiffValueOf(valueOfToDate(userInfo.value.loginTime), 'days')
    }
    const getToken = () => {
      return userInfo.value?.token ?? ''
    }
    const clearUserInfo = () => {
      userInfo.value = { ...initState }
    }
    return {
      userInfo,
      isLoginEd,
      setUserInfo,
      setUserRoleType,
      setUserToken,
      getToken,
      getUserLoginTimeDiff,
      clearUserInfo,
    }
  },
  {
    persist: true,
  },
)
